import json
import logging
from typing import List, Optional
from pydantic_ai import Agent
from .base_agent import BaseAgent
from models.data_models import SqlGenerationResult, AskHistory, Configuration, SqlFunction

logger = logging.getLogger(__name__)


class SqlGenerationAgent(BaseAgent):
    """SQL 生成 Agent"""
    
    def __init__(self):
        super().__init__()
        self.agent = self.create_agent(
            system_prompt=self._get_system_prompt(),
            result_type=SqlGenerationResult
        )
    
    def _get_system_prompt(self) -> str:
        return """
### TASK ###
You are an ANSI SQL expert with exceptional logical thinking skills. Your task is to generate syntactically correct and semantically meaningful SQL queries based on the user's question and the provided database schema.

### SQL GENERATION RULES ###
1. Always use proper ANSI SQL syntax
2. Use appropriate JOINs when multiple tables are involved
3. Include proper WHERE clauses for filtering
4. Use GROUP BY when aggregation is needed
5. Add ORDER BY for sorting when appropriate
6. Use proper data types and functions
7. Ensure the query is optimized and efficient
8. Handle NULL values appropriately
9. Use aliases for better readability
10. Follow naming conventions

### OUTPUT FORMAT ###
You must respond with a JSON object containing:
- sql: the generated SQL query as a string
- correlation_id: optional identifier for tracking (can be null)

### EXAMPLE ###
{
    "sql": "SELECT c.customer_name, SUM(o.total_amount) as total_sales FROM customers c JOIN orders o ON c.customer_id = o.customer_id GROUP BY c.customer_id, c.customer_name ORDER BY total_sales DESC",
    "correlation_id": null
}
"""
    
    async def generate_sql(
        self,
        query: str,
        table_ddls: List[str],
        histories: List[AskHistory] = None,
        sql_samples: List[dict] = None,
        instructions: List[dict] = None,
        configuration: Configuration = None,
        has_calculated_field: bool = False,
        has_metric: bool = False,
        sql_functions: List[SqlFunction] = None,
        error_feedback: Optional[str] = None
    ) -> SqlGenerationResult:
        """生成 SQL 查询"""
        
        try:
            self.log_agent_action("generate_sql", {
                "query": query,
                "table_count": len(table_ddls),
                "has_error_feedback": bool(error_feedback)
            })
            
            # 构建生成上下文
            context_parts = [f"User Question: {query}"]
            
            
            # 添加数据库结构
            if table_ddls:
                context_parts.append("Database Schema:")
                for i, ddl in enumerate(table_ddls):
                    context_parts.append(f"Table {i+1}:\n{ddl}")
            
            # 添加历史 SQL
            if histories:
                context_parts.append("Previous SQL Examples:")
                for i, history in enumerate(histories[-3:]):
                    context_parts.append(f"{i+1}. Q: {history.question}")
                    context_parts.append(f"   SQL: {history.sql}")
            
            # 添加 SQL 样例
            if sql_samples:
                context_parts.append("Similar SQL Examples:")
                for i, sample in enumerate(sql_samples[:3]):
                    if isinstance(sample, dict):
                        if 'question' in sample and 'sql' in sample:
                            context_parts.append(f"{i+1}. Q: {sample['question']}")
                            context_parts.append(f"   SQL: {sample['sql']}")
            
            # 添加特殊指令
            if instructions:
                context_parts.append("Special Instructions:")
                for i, instruction in enumerate(instructions):
                    if isinstance(instruction, dict) and 'instruction' in instruction:
                        context_parts.append(f"{i+1}. {instruction['instruction']}")
            
            # 添加计算字段提示
            if has_calculated_field:
                context_parts.append("Note: This query involves calculated fields. Use appropriate SQL functions and expressions.")
            
            # 添加指标提示
            if has_metric:
                context_parts.append("Note: This query involves metrics/KPIs. Use appropriate aggregation functions.")
            
            # 添加可用函数
            if sql_functions:
                context_parts.append("Available SQL Functions:")
                for func in sql_functions[:5]:  # 限制函数数量
                    context_parts.append(f"- {func.name}: {func.description}")
            
            # 添加错误反馈
            if error_feedback:
                context_parts.append(f"Previous Error to Fix: {error_feedback}")
                context_parts.append("Please generate a corrected SQL query that avoids this error.")
            
            prompt = "\n\n".join(context_parts)
            
            # 使用 Agent 生成 SQL
            result = await self.agent.run(prompt)
            
            self.log_agent_action("sql_generated", {
                "sql_length": len(result.data.sql),
                "has_correlation_id": bool(result.data.correlation_id)
            })
            
            return result.data
            
        except Exception as e:
            logger.error(f"SQL generation failed: {e}")
            # 返回错误 SQL
            return SqlGenerationResult(
                sql=f"-- Error generating SQL: {str(e)}\nSELECT 'Error' as message;",
                correlation_id=None
            )
