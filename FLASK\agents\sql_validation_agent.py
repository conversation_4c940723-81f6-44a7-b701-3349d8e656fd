import logging
import aiohttp
import sqlglot
from typing import Op<PERSON>, <PERSON><PERSON>
from .base_agent import BaseAgent
from models.data_models import SqlValidationResult
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import Config

logger = logging.getLogger(__name__)


class SqlValidationAgent(BaseAgent):
    """SQL 验证 Agent"""

    def __init__(self):
        super().__init__()

    async def validate_sql(
        self,
        sql: str,
        project_id: Optional[str] = None,
        timeout: float = None
    ) -> SqlValidationResult:
        """验证 SQL 查询"""

        try:
            self.log_agent_action("validate_sql", {
                "sql_length": len(sql),
                "project_id": project_id
            })

            timeout = timeout or Config.ENGINE_TIMEOUT

            # 第一步：语法验证
            syntax_valid, syntax_error = self._validate_syntax(sql)
            if not syntax_valid:
                return SqlValidationResult(
                    is_valid=False,
                    error_message=syntax_error,
                    error_type="SYNTAX"
                )

            # 第二步：执行验证（dry run）
            execution_valid, execution_error = await self._validate_execution(
                sql, project_id, timeout
            )

            if execution_valid:
                self.log_agent_action("sql_validated", {"status": "success"})
                return SqlValidationResult(is_valid=True)
            else:
                error_type = "TIMEOUT" if "timeout" in execution_error.lower() else "EXECUTION"
                self.log_agent_action("sql_validation_failed", {
                    "error_type": error_type,
                    "error": execution_error[:100]
                })
                return SqlValidationResult(
                    is_valid=False,
                    error_message=execution_error,
                    error_type=error_type
                )

        except Exception as e:
            logger.error(f"SQL validation failed: {e}")
            return SqlValidationResult(
                is_valid=False,
                error_message=f"Validation error: {str(e)}",
                error_type="EXECUTION"
            )

    def _validate_syntax(self, sql: str) -> Tuple[bool, Optional[str]]:
        """验证 SQL 语法"""
        try:
            # 使用 sqlglot 进行语法验证和格式化
            quoted_sql = sqlglot.transpile(
                sql,
                read="trino",
                identify=True,
                error_level=sqlglot.ErrorLevel.RAISE
            )[0]
            return True, None
        except Exception as e:
            return False, f"Syntax error: {str(e)}"

    async def _validate_execution(
        self,
        sql: str,
        project_id: Optional[str],
        timeout: float
    ) -> Tuple[bool, Optional[str]]:
        """验证 SQL 执行（dry run）"""
        try:
            # 清理 SQL（移除末尾分号）
            cleaned_sql = sql.rstrip(";") if sql.endswith(";") else sql

            # 添加引号
            quoted_sql, quote_error = self._add_quotes(cleaned_sql)
            if quote_error:
                return False, f"Quote error: {quote_error}"

            # 调用 Wren Engine 进行 dry run
            async with aiohttp.ClientSession() as session:
                # 这里使用 DuckDB 的验证端点作为示例
                async with session.get(
                    f"{Config.WREN_ENGINE_ENDPOINT}/v1/mdl/dry-run",
                    json={
                        "sql": self._remove_limit_statement(quoted_sql),
                        "manifest": {},  # 这里应该传入实际的 MDL
                        "limit": 1,
                    },
                    timeout=aiohttp.ClientTimeout(total=timeout),
                ) as response:
                    if response.status == 200:
                        return True, None
                    else:
                        error_text = await response.text()
                        return False, error_text

        except aiohttp.ClientTimeout:
            return False, "Request timed out"
        except Exception as e:
            return False, f"Execution validation error: {str(e)}"

    def _add_quotes(self, sql: str) -> Tuple[str, Optional[str]]:
        """为 SQL 添加引号"""
        try:
            quoted_sql = sqlglot.transpile(
                sql, read="trino", identify=True, error_level=sqlglot.ErrorLevel.RAISE
            )[0]
            return quoted_sql, None
        except Exception as e:
            return "", str(e)

    def _remove_limit_statement(self, sql: str) -> str:
        """移除 LIMIT 语句"""
        # 简单的 LIMIT 移除逻辑
        lines = sql.split('\n')
        filtered_lines = []
        for line in lines:
            if not line.strip().upper().startswith('LIMIT'):
                filtered_lines.append(line)
        return '\n'.join(filtered_lines)
