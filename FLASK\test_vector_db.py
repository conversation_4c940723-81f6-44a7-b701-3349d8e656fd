#!/usr/bin/env python3
"""
测试向量数据库功能的脚本
"""

import requests
import json
import time
import sys


class VectorDBTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_store_question_sql(self):
        """测试存储问题-SQL对"""
        print("🔍 Testing store question-SQL pair...")
        
        test_data = {
            "question": "How many customers do we have in total?",
            "sql": "SELECT COUNT(*) FROM customers",
            "project_id": "test_project",
            "metadata": {
                "test": True,
                "category": "count_query"
            }
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/vector-db/store",
                json=test_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Store question-SQL pair successful")
                print(f"   Doc ID: {result.get('doc_id')}")
                print(f"   Message: {result.get('message')}")
                return result.get('doc_id')
            else:
                print(f"❌ Store failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Store error: {e}")
            return None
    
    def test_vector_db_stats(self):
        """测试获取向量数据库统计信息"""
        print("\n📊 Testing vector DB stats...")
        
        try:
            response = self.session.get(f"{self.base_url}/v1/vector-db/stats")
            
            if response.status_code == 200:
                stats = response.json()
                print("✅ Vector DB stats retrieved")
                print(f"   Total records: {stats.get('total_records')}")
                print(f"   Collection name: {stats.get('collection_name')}")
                print(f"   Embedding model: {stats.get('embedding_model')}")
                print(f"   Similarity threshold: {stats.get('similarity_threshold')}")
                if stats.get('avg_feedback_per_record'):
                    print(f"   Avg feedback per record: {stats.get('avg_feedback_per_record')}")
                if stats.get('helpful_rate'):
                    print(f"   Helpful rate: {stats.get('helpful_rate')}")
                return True
            else:
                print(f"❌ Stats retrieval failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Stats retrieval error: {e}")
            return False
    
    def test_ask_with_historical_retrieval(self):
        """测试带历史检索的问答"""
        print("\n🤖 Testing ask with historical retrieval...")
        
        # 先存储一些测试数据
        test_questions = [
            {
                "question": "How many customers are there?",
                "sql": "SELECT COUNT(*) FROM customers",
                "project_id": "test_project"
            },
            {
                "question": "What is the total sales amount?",
                "sql": "SELECT SUM(total_amount) FROM orders",
                "project_id": "test_project"
            },
            {
                "question": "Show me the top customers by order value",
                "sql": "SELECT c.customer_name, SUM(o.total_amount) as total FROM customers c JOIN orders o ON c.customer_id = o.customer_id GROUP BY c.customer_id ORDER BY total DESC LIMIT 10",
                "project_id": "test_project"
            }
        ]
        
        print("   Storing test data...")
        for data in test_questions:
            self.session.post(
                f"{self.base_url}/v1/vector-db/store",
                json=data,
                headers={"Content-Type": "application/json"}
            )
        
        # 测试相似问题检索
        similar_question = "How many customers do we have in total?"
        
        ask_data = {
            "query": similar_question,
            "project_id": "test_project",
            "configurations": {
                "language": "English"
            }
        }
        
        try:
            print(f"   Asking similar question: '{similar_question}'")
            response = self.session.post(
                f"{self.base_url}/v1/asks",
                json=ask_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                query_id = response.json()["query_id"]
                print(f"   Query ID: {query_id}")
                
                # 等待处理完成
                time.sleep(3)
                
                # 获取结果
                result_response = self.session.get(f"{self.base_url}/v1/asks/{query_id}/result")
                if result_response.status_code == 200:
                    result = result_response.json()
                    print(f"   Status: {result.get('status')}")
                    
                    if result.get('response'):
                        for i, resp in enumerate(result['response']):
                            print(f"   SQL {i+1}: {resp.get('sql')}")
                            print(f"   Type: {resp.get('type')}")
                    
                    return query_id
                else:
                    print(f"   ❌ Failed to get result: {result_response.status_code}")
                    return None
            else:
                print(f"   ❌ Ask failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"   ❌ Ask error: {e}")
            return None
    
    def test_feedback_submission(self, query_id):
        """测试反馈提交"""
        if not query_id:
            print("\n⚠️  Skipping feedback test - no query ID")
            return False
        
        print(f"\n👍 Testing feedback submission for query {query_id}...")
        
        feedback_data = {
            "is_helpful": True,
            "comment": "This SQL query is exactly what I needed!"
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/asks/{query_id}/feedback",
                json=feedback_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Feedback submitted successfully")
                print(f"   Success: {result.get('success')}")
                print(f"   Message: {result.get('message')}")
                return True
            else:
                print(f"❌ Feedback submission failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Feedback submission error: {e}")
            return False
    
    def test_unhelpful_feedback(self, query_id):
        """测试负面反馈"""
        if not query_id:
            print("\n⚠️  Skipping unhelpful feedback test - no query ID")
            return False
        
        print(f"\n👎 Testing unhelpful feedback for query {query_id}...")
        
        feedback_data = {
            "is_helpful": False,
            "comment": "This SQL doesn't match what I was looking for"
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/v1/asks/{query_id}/feedback",
                json=feedback_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Unhelpful feedback submitted successfully")
                print(f"   Success: {result.get('success')}")
                print(f"   Message: {result.get('message')}")
                return True
            else:
                print(f"❌ Unhelpful feedback submission failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Unhelpful feedback submission error: {e}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 Starting Vector DB Tests\n")
        
        success_count = 0
        total_tests = 5
        
        # 测试1: 存储问题-SQL对
        if self.test_store_question_sql():
            success_count += 1
        
        # 测试2: 获取统计信息
        if self.test_vector_db_stats():
            success_count += 1
        
        # 测试3: 带历史检索的问答
        query_id = self.test_ask_with_historical_retrieval()
        if query_id:
            success_count += 1
        
        # 测试4: 正面反馈
        if self.test_feedback_submission(query_id):
            success_count += 1
        
        # 测试5: 负面反馈
        if self.test_unhelpful_feedback(query_id):
            success_count += 1
        
        # 最终统计
        print(f"\n{'='*50}")
        if self.test_vector_db_stats():  # 再次查看统计信息
            pass
        
        print(f"\n📊 Test Summary: {success_count}/{total_tests} tests passed")
        
        return success_count == total_tests


def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"
    
    print(f"Testing Vector DB functionality at: {base_url}")
    
    tester = VectorDBTester(base_url)
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 All vector DB tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Some vector DB tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
