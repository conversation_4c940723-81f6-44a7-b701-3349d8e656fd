from typing import Optional, List
from pydantic import BaseModel, Field
from .data_models import Ask<PERSON>istory, Configuration


class AskRequest(BaseModel):
    query: str
    project_id: Optional[str] = None
    mdl_hash: Optional[str] = None
    thread_id: Optional[str] = None
    histories: List[AskHistory] = Field(default_factory=list)
    configurations: Configuration = Field(default_factory=Configuration)
    ignore_sql_generation_reasoning: bool = False
    enable_column_pruning: bool = False


class StopAskRequest(BaseModel):
    status: str = "stopped"


class AskResultRequest(BaseModel):
    query_id: str


class FeedbackRequest(BaseModel):
    query_id: str
    is_helpful: bool
    comment: Optional[str] = None


class StoreQuestionSqlRequest(BaseModel):
    question: str
    sql: str
    project_id: Optional[str] = None
    view_id: Optional[str] = None
    metadata: Optional[dict] = None
