<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wren AI - 智能SQL生成助手</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🤖 Wren AI</h1>
            <p>基于 PydanticAI Agent Workflow 的智能SQL生成助手</p>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 警告消息区域 -->
            <div id="alerts"></div>

            <!-- 标签页导航 -->
            <div class="tabs">
                <div class="tab active" data-tab="askTab">💬 智能问答</div>
                <div class="tab" data-tab="storeTab">💾 数据管理</div>
                <div class="tab" data-tab="statsTab">📊 统计信息</div>
            </div>

            <!-- 智能问答标签页 -->
            <div id="askTab" class="tab-content active">
                <div class="section">
                    <h2>💬 智能问答</h2>
                    <p>输入您的问题，AI将为您生成相应的SQL查询。系统会首先检索历史问题，如果找到相似问题将直接返回结果。</p>

                    <form id="askForm">
                        <div class="form-group">
                            <label for="userQuery">您的问题 *</label>
                            <textarea id="userQuery" class="form-control"
                                placeholder="例如：How many customers do we have? 或 What is the total sales amount?"
                                rows="3" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="projectId">项目ID（可选）</label>
                            <input type="text" id="projectId" class="form-control"
                                placeholder="例如：demo_project">
                        </div>

                        <button type="submit" class="btn btn-primary">
                            🚀 提交问题
                        </button>

                        <button type="button" id="clearResults" class="btn btn-secondary">
                            🗑️ 清空结果
                        </button>
                    </form>

                    <!-- 加载状态 -->
                    <div id="loading" class="loading">
                        <div class="spinner"></div>
                        <p id="loadingMessage">处理中...</p>
                        <div id="currentStatus"></div>
                    </div>

                    <!-- 查询结果 -->
                    <div id="queryResult"></div>
                </div>
            </div>

            <!-- 数据管理标签页 -->
            <div id="storeTab" class="tab-content">
                <div class="section">
                    <h2>💾 数据管理</h2>
                    <p>手动添加问题-SQL对到向量数据库，用于改进系统的检索能力。</p>

                    <form id="storeForm">
                        <div class="form-group">
                            <label for="storeQuestion">问题 *</label>
                            <textarea id="storeQuestion" class="form-control"
                                placeholder="例如：How many orders were placed today?"
                                rows="2" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="storeSQL">SQL查询 *</label>
                            <textarea id="storeSQL" class="form-control"
                                placeholder="例如：SELECT COUNT(*) FROM orders WHERE DATE(order_date) = CURRENT_DATE"
                                rows="4" required></textarea>
                        </div>

                        <div class="form-group">
                            <label for="storeProjectId">项目ID（可选）</label>
                            <input type="text" id="storeProjectId" class="form-control"
                                placeholder="例如：demo_project">
                        </div>

                        <div class="form-group">
                            <label for="storeViewId">视图ID（可选）</label>
                            <input type="text" id="storeViewId" class="form-control"
                                placeholder="例如：daily_orders_view">
                        </div>

                        <button type="submit" id="storeBtn" class="btn btn-primary">
                            💾 存储到向量数据库
                        </button>
                    </form>
                </div>

                <div class="section">
                    <h2>📋 使用说明</h2>
                    <div class="alert alert-info">
                        <h4>💡 系统工作流程：</h4>
                        <ol>
                            <li><strong>历史检索：</strong>首先在向量数据库中搜索相似的历史问题</li>
                            <li><strong>直接返回：</strong>如果找到相似度高的问题，直接返回对应的SQL</li>
                            <li><strong>智能生成：</strong>如果没有找到，执行完整的AI生成流程</li>
                            <li><strong>自动学习：</strong>成功生成的SQL会自动存储到向量数据库</li>
                            <li><strong>反馈优化：</strong>用户反馈会影响后续的检索质量</li>
                        </ol>

                        <h4>🎯 反馈机制：</h4>
                        <ul>
                            <li>点击"👍 有帮助"会增加该问题-SQL对的权重</li>
                            <li>点击"👎 没帮助"会降低该问题-SQL对的权重</li>
                            <li>反馈数据用于持续改进系统性能</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 统计信息标签页 -->
            <div id="statsTab" class="tab-content">
                <div class="section">
                    <h2>📊 向量数据库统计</h2>
                    <p>查看向量数据库的使用情况和性能指标。</p>

                    <button id="refreshStats" class="btn btn-primary">
                        🔄 刷新统计
                    </button>

                    <div id="vectorDBStats">
                        <p>加载中...</p>
                    </div>
                </div>

                <div class="section">
                    <h2>🔧 系统信息</h2>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">PydanticAI</div>
                            <div class="stat-label">Agent框架</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">DeepSeek</div>
                            <div class="stat-label">LLM模型</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">Chroma</div>
                            <div class="stat-label">向量数据库</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">Flask</div>
                            <div class="stat-label">API框架</div>
                        </div>
                    </div>

                    <div style="margin-top: 20px;">
                        <h4>🤖 Agent工作流：</h4>
                        <ol>
                            <li><strong>HistoricalQuestionAgent</strong> - 历史问题检索</li>
                            <li><strong>IntentAgent</strong> - 意图分类</li>
                            <li><strong>SchemaRetrievalAgent</strong> - 表结构检索</li>
                            <li><strong>SqlGenerationAgent</strong> - SQL生成</li>
                            <li><strong>SqlValidationAgent</strong> - SQL验证</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <div style="text-align: center; padding: 20px; color: white; opacity: 0.8;">
        <p>Powered by Wren AI Flask Service | Built with PydanticAI + DeepSeek + Chroma</p>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
