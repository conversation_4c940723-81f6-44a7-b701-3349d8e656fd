import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # DeepSeek API 配置
    DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
    DEEPSEEK_BASE_URL = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com/v1")
    DEEPSEEK_MODEL = os.getenv("DEEPSEEK_MODEL", "deepseek-chat")
    
    # Flask 配置
    SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-here")
    DEBUG = os.getenv("DEBUG", "False").lower() == "true"
    
    # 数据库和引擎配置
    WREN_ENGINE_ENDPOINT = os.getenv("WREN_ENGINE_ENDPOINT", "http://localhost:8080")
    WREN_IBIS_ENDPOINT = os.getenv("WREN_IBIS_ENDPOINT", "http://localhost:8000")
    
    # 缓存配置
    CACHE_TTL = int(os.getenv("CACHE_TTL", "120"))
    CACHE_MAXSIZE = int(os.getenv("CACHE_MAXSIZE", "1000000"))
    
    # 重试配置
    MAX_SQL_CORRECTION_RETRIES = int(os.getenv("MAX_SQL_CORRECTION_RETRIES", "3"))
    ENGINE_TIMEOUT = float(os.getenv("ENGINE_TIMEOUT", "30.0"))
    
    # 检索配置
    TABLE_RETRIEVAL_SIZE = int(os.getenv("TABLE_RETRIEVAL_SIZE", "10"))
    TABLE_COLUMN_RETRIEVAL_SIZE = int(os.getenv("TABLE_COLUMN_RETRIEVAL_SIZE", "20"))
    
    # Langfuse 配置
    LANGFUSE_PUBLIC_KEY = os.getenv("LANGFUSE_PUBLIC_KEY", "")
    LANGFUSE_SECRET_KEY = os.getenv("LANGFUSE_SECRET_KEY", "")
    LANGFUSE_HOST = os.getenv("LANGFUSE_HOST", "")
