import logging
from typing import Any, Dict, Optional
from openai import Async<PERSON>penA<PERSON>
from pydantic_ai import Agent
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import Config

logger = logging.getLogger(__name__)


class BaseAgent:
    """所有 Agent 的基类"""

    def __init__(self, model_name: str = None):
        self.client = AsyncOpenAI(
            api_key=Config.DEEPSEEK_API_KEY,
            base_url=Config.DEEPSEEK_BASE_URL
        )
        self.model_name = model_name or Config.DEEPSEEK_MODEL

    def create_agent(self, system_prompt: str, result_type: Any = str) -> Agent:
        """创建 PydanticAI Agent"""
        return Agent(
            model=self.model_name,
            result_type=result_type,
            system_prompt=system_prompt,
            model_settings={
                "api_key": Config.DEEPSEEK_API_KEY,
                "base_url": Config.DEEPSEEK_BASE_URL,
                "temperature": 0.1,
                "max_tokens": 4000
            }
        )

    async def call_llm(self, prompt: str, system_prompt: str = "", **kwargs) -> str:
        """直接调用 LLM"""
        try:
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            response = await self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                temperature=kwargs.get("temperature", 0.1),
                max_tokens=kwargs.get("max_tokens", 4000)
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            raise

    def log_agent_action(self, action: str, details: Dict[str, Any]):
        """记录 Agent 行为"""
        logger.info(f"Agent Action: {action}, Details: {details}")
