import logging
import uuid
import hashlib
from typing import List, Dict, Optional, Any
from datetime import datetime
import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
from config import Config

logger = logging.getLogger(__name__)


class VectorDBService:
    """向量数据库服务 - 基于 Chroma"""
    
    def __init__(self):
        self.persist_directory = Config.CHROMA_PERSIST_DIRECTORY
        self.collection_name = Config.CHROMA_COLLECTION_NAME
        self.embedding_model_name = Config.EMBEDDING_MODEL
        self.similarity_threshold = Config.SIMILARITY_THRESHOLD
        self.max_results = Config.MAX_HISTORICAL_RESULTS
        
        # 初始化 Chroma 客户端
        self.client = chromadb.PersistentClient(
            path=self.persist_directory,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # 初始化嵌入模型
        self.embedding_model = SentenceTransformer(self.embedding_model_name)
        
        # 获取或创建集合
        self.collection = self._get_or_create_collection()
        
        logger.info(f"VectorDB initialized with collection: {self.collection_name}")
    
    def _get_or_create_collection(self):
        """获取或创建集合"""
        try:
            # 尝试获取现有集合
            collection = self.client.get_collection(name=self.collection_name)
            logger.info(f"Found existing collection: {self.collection_name}")
        except Exception:
            # 创建新集合
            collection = self.client.create_collection(
                name=self.collection_name,
                metadata={"description": "Historical questions and SQL pairs"}
            )
            logger.info(f"Created new collection: {self.collection_name}")
        
        return collection
    
    def _generate_document_id(self, question: str, project_id: Optional[str] = None) -> str:
        """生成文档ID"""
        # 使用问题和项目ID的哈希作为唯一标识
        content = f"{question}_{project_id or 'default'}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _create_embedding(self, text: str) -> List[float]:
        """创建文本嵌入"""
        try:
            embedding = self.embedding_model.encode(text, convert_to_tensor=False)
            return embedding.tolist()
        except Exception as e:
            logger.error(f"Failed to create embedding: {e}")
            raise
    
    async def store_question_sql_pair(
        self,
        question: str,
        sql: str,
        project_id: Optional[str] = None,
        view_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """存储问题-SQL对"""
        
        try:
            doc_id = self._generate_document_id(question, project_id)
            
            # 创建嵌入
            embedding = self._create_embedding(question)
            
            # 准备元数据
            doc_metadata = {
                "project_id": project_id or "default",
                "view_id": view_id,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "feedback_count": 1,  # 初始反馈计数
                "helpful_count": 1,   # 有用反馈计数
                "unhelpful_count": 0, # 无用反馈计数
                "sql_length": len(sql),
                "question_length": len(question)
            }
            
            # 添加额外元数据
            if metadata:
                doc_metadata.update(metadata)
            
            # 检查是否已存在
            existing = self.collection.get(ids=[doc_id])
            
            if existing['ids']:
                # 更新现有记录
                await self._update_existing_record(doc_id, question, sql, doc_metadata)
                logger.info(f"Updated existing record: {doc_id}")
            else:
                # 添加新记录
                self.collection.add(
                    ids=[doc_id],
                    embeddings=[embedding],
                    documents=[question],
                    metadatas=[{
                        **doc_metadata,
                        "sql": sql  # SQL 存储在元数据中
                    }]
                )
                logger.info(f"Added new record: {doc_id}")
            
            return doc_id
            
        except Exception as e:
            logger.error(f"Failed to store question-SQL pair: {e}")
            raise
    
    async def _update_existing_record(
        self,
        doc_id: str,
        question: str,
        sql: str,
        new_metadata: Dict[str, Any]
    ):
        """更新现有记录"""
        
        try:
            # 获取现有记录
            existing = self.collection.get(ids=[doc_id], include=["metadatas"])
            
            if existing['metadatas']:
                old_metadata = existing['metadatas'][0]
                
                # 更新反馈计数
                new_metadata.update({
                    "feedback_count": old_metadata.get("feedback_count", 0) + 1,
                    "helpful_count": old_metadata.get("helpful_count", 0) + 1,
                    "updated_at": datetime.now().isoformat(),
                    "sql": sql  # 更新 SQL
                })
                
                # 更新记录
                self.collection.update(
                    ids=[doc_id],
                    metadatas=[new_metadata]
                )
                
        except Exception as e:
            logger.error(f"Failed to update existing record: {e}")
            raise
    
    async def search_similar_questions(
        self,
        question: str,
        project_id: Optional[str] = None,
        n_results: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """搜索相似问题"""
        
        try:
            # 创建查询嵌入
            query_embedding = self._create_embedding(question)
            
            # 设置结果数量
            n_results = n_results or self.max_results
            
            # 构建查询条件
            where_clause = {}
            if project_id:
                where_clause["project_id"] = project_id
            
            # 执行搜索
            results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where=where_clause if where_clause else None,
                include=["documents", "metadatas", "distances"]
            )
            
            # 处理结果
            similar_questions = []
            
            if results['ids'] and results['ids'][0]:
                for i, doc_id in enumerate(results['ids'][0]):
                    distance = results['distances'][0][i]
                    similarity = 1 - distance  # 转换为相似度
                    
                    # 过滤低相似度结果
                    if similarity >= self.similarity_threshold:
                        metadata = results['metadatas'][0][i]
                        
                        similar_questions.append({
                            "id": doc_id,
                            "question": results['documents'][0][i],
                            "statement": metadata.get("sql", ""),
                            "viewId": metadata.get("view_id"),
                            "similarity": round(similarity, 3),
                            "project_id": metadata.get("project_id"),
                            "feedback_count": metadata.get("feedback_count", 0),
                            "helpful_count": metadata.get("helpful_count", 0),
                            "created_at": metadata.get("created_at"),
                            "updated_at": metadata.get("updated_at")
                        })
            
            logger.info(f"Found {len(similar_questions)} similar questions for: {question[:50]}...")
            return similar_questions
            
        except Exception as e:
            logger.error(f"Failed to search similar questions: {e}")
            return []
    
    async def update_feedback(
        self,
        question: str,
        is_helpful: bool,
        project_id: Optional[str] = None
    ) -> bool:
        """更新反馈"""
        
        try:
            doc_id = self._generate_document_id(question, project_id)
            
            # 获取现有记录
            existing = self.collection.get(ids=[doc_id], include=["metadatas"])
            
            if not existing['ids']:
                logger.warning(f"No record found for feedback update: {doc_id}")
                return False
            
            # 更新元数据
            metadata = existing['metadatas'][0]
            metadata.update({
                "feedback_count": metadata.get("feedback_count", 0) + 1,
                "updated_at": datetime.now().isoformat()
            })
            
            if is_helpful:
                metadata["helpful_count"] = metadata.get("helpful_count", 0) + 1
            else:
                metadata["unhelpful_count"] = metadata.get("unhelpful_count", 0) + 1
            
            # 更新记录
            self.collection.update(
                ids=[doc_id],
                metadatas=[metadata]
            )
            
            logger.info(f"Updated feedback for: {doc_id}, helpful: {is_helpful}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update feedback: {e}")
            return False
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """获取集合统计信息"""
        
        try:
            count = self.collection.count()
            
            # 获取一些样本数据来计算统计信息
            sample_data = self.collection.get(
                limit=min(100, count),
                include=["metadatas"]
            )
            
            stats = {
                "total_records": count,
                "collection_name": self.collection_name,
                "embedding_model": self.embedding_model_name,
                "similarity_threshold": self.similarity_threshold
            }
            
            if sample_data['metadatas']:
                # 计算反馈统计
                total_feedback = sum(m.get("feedback_count", 0) for m in sample_data['metadatas'])
                total_helpful = sum(m.get("helpful_count", 0) for m in sample_data['metadatas'])
                
                stats.update({
                    "avg_feedback_per_record": round(total_feedback / len(sample_data['metadatas']), 2),
                    "helpful_rate": round(total_helpful / max(total_feedback, 1), 2)
                })
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {"error": str(e)}
    
    def reset_collection(self):
        """重置集合（仅用于测试）"""
        try:
            self.client.delete_collection(name=self.collection_name)
            self.collection = self._get_or_create_collection()
            logger.info(f"Reset collection: {self.collection_name}")
        except Exception as e:
            logger.error(f"Failed to reset collection: {e}")
            raise
