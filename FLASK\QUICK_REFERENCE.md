# Wren AI Flask Service - 快速参考指南

## 🚀 快速开始

### 启动服务
```bash
# 1. 初始化向量数据库
python init_vector_db.py

# 2. 启动服务
python start.py

# 3. 访问前端
# 主页面: http://localhost:5000
# 演示页面: http://localhost:5000/demo
```

### 测试服务
```bash
python test_api.py              # API测试
python test_vector_db.py        # 向量数据库测试
python test_frontend.py         # 前端测试
python example_usage.py         # 使用示例
```

## 📋 核心API速查

### 提交问题
```bash
curl -X POST http://localhost:5000/v1/asks \
  -H "Content-Type: application/json" \
  -d '{"query": "How many customers do we have?"}'
```

### 获取结果
```bash
curl http://localhost:5000/v1/asks/{query_id}/result
```

### 提交反馈
```bash
curl -X POST http://localhost:5000/v1/asks/{query_id}/feedback \
  -H "Content-Type: application/json" \
  -d '{"is_helpful": true}'
```

### 存储问题-SQL对
```bash
curl -X POST http://localhost:5000/v1/vector-db/store \
  -H "Content-Type: application/json" \
  -d '{
    "question": "How many orders today?",
    "sql": "SELECT COUNT(*) FROM orders WHERE DATE(order_date) = CURRENT_DATE"
  }'
```

### 查看统计
```bash
curl http://localhost:5000/v1/vector-db/stats
```

## 🤖 Agent工作流

### 完整流程
```
用户问题 → 历史检索 → 意图分类 → 表结构检索 → SQL推理 → SQL生成 → SQL验证 → 返回结果
```

### 状态转换
```
understanding → searching → reasoning → generating → validating → finished
```

### 重试机制
- **最大重试次数**: 3次
- **重试触发**: SQL验证失败
- **错误反馈**: 传递给下次生成

## 📊 数据模型速查

### 请求模型
```python
# 问答请求
{
    "query": "string",              # 必需
    "project_id": "string",         # 可选
    "configurations": {...}         # 可选
}

# 反馈请求
{
    "query_id": "string",           # 必需
    "is_helpful": boolean,          # 必需
    "comment": "string"             # 可选
}

# 存储请求
{
    "question": "string",           # 必需
    "sql": "string",               # 必需
    "project_id": "string",        # 可选
    "metadata": {...}              # 可选
}
```

### 响应模型
```python
# 问答响应
{
    "status": "finished|failed|...",
    "response": [{"sql": "...", "type": "llm"}],
    "rephrased_question": "string",
    "intent_reasoning": "string",
    "sql_generation_reasoning": "string"
}

# 统计响应
{
    "total_records": 150,
    "collection_name": "historical_questions",
    "similarity_threshold": 0.7,
    "helpful_rate": 0.85
}
```

## ⚙️ 配置速查

### 环境变量
```bash
# 必需配置
DEEPSEEK_API_KEY=your-api-key

# 可选配置
DEEPSEEK_MODEL=deepseek-chat
CHROMA_PERSIST_DIRECTORY=./chroma_db
SIMILARITY_THRESHOLD=0.7
MAX_SQL_CORRECTION_RETRIES=3
```

### 性能参数
```python
CACHE_TTL = 120                 # 缓存生存时间(秒)
CACHE_MAXSIZE = 1000000         # 缓存最大大小
ENGINE_TIMEOUT = 30.0           # 引擎超时时间(秒)
MAX_HISTORICAL_RESULTS = 3      # 最大历史结果数
```

## 🔧 故障排除

### 常见问题
1. **服务启动失败**
   - 检查API密钥配置
   - 确认端口5000未被占用
   - 查看错误日志

2. **SQL生成失败**
   - 检查DeepSeek API连接
   - 查看重试次数和错误信息
   - 验证问题格式

3. **向量数据库错误**
   - 检查存储目录权限
   - 确认嵌入模型下载
   - 重新初始化数据库

4. **前端无法访问**
   - 确认服务已启动
   - 检查浏览器控制台错误
   - 验证静态文件路径

### 调试命令
```bash
# 查看服务状态
curl http://localhost:5000/health

# 查看工作流详情 (调试模式)
curl http://localhost:5000/v1/debug/workflow/{query_id}

# 查看日志
tail -f wren_ai.log

# 检查向量数据库
python -c "
import chromadb
client = chromadb.PersistentClient(path='./chroma_db')
print(client.list_collections())
"
```

## 📈 监控指标

### 关键指标
- **响应时间**: API < 100ms, SQL生成 < 30s
- **成功率**: > 90%
- **向量检索**: < 1s
- **内存使用**: < 2GB

### 监控端点
```bash
# 健康检查
GET /health

# 向量数据库统计
GET /v1/vector-db/stats

# 查询状态
GET /v1/asks/{query_id}/status
```

## 🔒 安全检查清单

### 部署前检查
- [ ] API密钥已设置且有效
- [ ] 敏感信息不在代码中
- [ ] 输入验证已启用
- [ ] 日志记录已配置
- [ ] 错误处理已完善

### 运行时监控
- [ ] API调用频率正常
- [ ] 错误率在可接受范围
- [ ] 资源使用未超限
- [ ] 日志无异常访问

## 📚 开发指南

### 添加新Agent
1. 继承`BaseAgent`类
2. 实现核心方法
3. 定义输入输出模型
4. 添加到工作流服务
5. 编写单元测试

### 扩展API端点
1. 定义请求/响应模型
2. 实现Flask路由
3. 添加错误处理
4. 更新API文档
5. 编写集成测试

### 优化性能
1. 使用缓存机制
2. 并行执行任务
3. 优化数据库查询
4. 减少LLM调用
5. 监控性能指标

## 🧪 测试策略

### 测试层级
```
单元测试 → 集成测试 → 端到端测试 → 性能测试
```

### 测试覆盖
- **Agent功能**: 每个Agent的核心方法
- **API端点**: 所有REST接口
- **工作流**: 完整的执行流程
- **错误处理**: 各种异常情况
- **前端功能**: 用户交互场景

### 测试数据
```python
# 示例问题
test_questions = [
    "How many customers do we have?",
    "What is the total sales amount?",
    "Show me top customers",
    "Monthly sales trends"
]

# 预期SQL模式
expected_patterns = [
    "SELECT COUNT(*) FROM customers",
    "SELECT SUM(.*) FROM .*",
    "ORDER BY .* DESC LIMIT",
    "GROUP BY .*month.*"
]
```

## 📖 文档结构

```
FUNCTIONAL_SPECIFICATION.md     # 完整功能式样书
FUNCTIONAL_SPECIFICATION_TOC.md # 文档目录
QUICK_REFERENCE.md             # 本快速参考
README.md                      # 项目说明
FRONTEND_GUIDE.md              # 前端使用指南
```

## 🔗 相关链接

- **主要文档**: [FUNCTIONAL_SPECIFICATION.md](./FUNCTIONAL_SPECIFICATION.md)
- **前端指南**: [FRONTEND_GUIDE.md](./FRONTEND_GUIDE.md)
- **项目说明**: [README.md](./README.md)
- **PydanticAI**: https://ai.pydantic.dev/
- **DeepSeek API**: https://platform.deepseek.com/api-docs/
- **Chroma DB**: https://docs.trychroma.com/

---

*快速参考指南最后更新时间: 2024年*
