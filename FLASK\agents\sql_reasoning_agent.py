import logging
from typing import List, Optional
from pydantic_ai import Agent
from .base_agent import BaseAgent
from models.data_models import SqlReasoningResult, AskHistory, Configuration

logger = logging.getLogger(__name__)


class SqlReasoningAgent(BaseAgent):
    """SQL 推理 Agent"""
    
    def __init__(self):
        super().__init__()
        self.agent = self.create_agent(
            system_prompt=self._get_system_prompt(),
            result_type=SqlReasoningResult
        )
    
    def _get_system_prompt(self) -> str:
        return """
### TASK ###
You are a helpful data analyst who is great at thinking deeply and reasoning about the user's question and the database schema, and you provide a step-by-step reasoning plan in order to answer the user's question.

### INSTRUCTIONS ###
1. Analyze the user's question carefully
2. Examine the provided database schema
3. Think step by step about how to answer the question
4. Provide a clear reasoning process
5. Break down the solution into logical steps

### OUTPUT FORMAT ###
You must respond with a JSON object containing:
- reasoning: detailed explanation of your thought process
- step_by_step_plan: array of steps to solve the problem

### EXAMPLE ###
{
    "reasoning": "The user wants to find the total sales for each customer. I need to join the customers table with orders table and sum the total_amount.",
    "step_by_step_plan": [
        "Join customers table with orders table on customer_id",
        "Group by customer information",
        "Sum the total_amount for each customer",
        "Order by total sales descending"
    ]
}
"""
    
    async def generate_reasoning(
        self,
        query: str,
        table_ddls: List[str],
        histories: List[AskHistory] = None,
        sql_samples: List[dict] = None,
        instructions: List[dict] = None,
        configuration: Configuration = None,
        error_feedback: Optional[str] = None
    ) -> SqlReasoningResult:
        """生成 SQL 推理"""
        
        try:
            self.log_agent_action("generate_reasoning", {
                "query": query,
                "table_count": len(table_ddls),
                "has_error_feedback": bool(error_feedback)
            })
            
            # 构建推理上下文
            context_parts = [f"User Question: {query}"]
            
            # 添加数据库结构
            if table_ddls:
                context_parts.append("Database Schema:")
                for i, ddl in enumerate(table_ddls):
                    context_parts.append(f"Table {i+1}:\n{ddl}")
            
            # 添加历史对话
            if histories:
                context_parts.append("Previous Questions and SQL:")
                for i, history in enumerate(histories[-3:]):
                    context_parts.append(f"{i+1}. Q: {history.question}")
                    context_parts.append(f"   SQL: {history.sql}")
            
            # 添加 SQL 样例
            if sql_samples:
                context_parts.append("Similar SQL Examples:")
                for i, sample in enumerate(sql_samples[:3]):
                    if isinstance(sample, dict):
                        if 'question' in sample and 'sql' in sample:
                            context_parts.append(f"{i+1}. Q: {sample['question']}")
                            context_parts.append(f"   SQL: {sample['sql']}")
            
            # 添加指令
            if instructions:
                context_parts.append("Special Instructions:")
                for i, instruction in enumerate(instructions):
                    if isinstance(instruction, dict) and 'instruction' in instruction:
                        context_parts.append(f"{i+1}. {instruction['instruction']}")
            
            # 添加错误反馈
            if error_feedback:
                context_parts.append(f"Previous Error to Fix: {error_feedback}")
                context_parts.append("Please revise your reasoning to avoid this error.")
            
            prompt = "\n\n".join(context_parts)
            
            # 使用 Agent 生成推理
            result = await self.agent.run(prompt)
            
            self.log_agent_action("reasoning_generated", {
                "reasoning_length": len(result.data.reasoning),
                "step_count": len(result.data.step_by_step_plan)
            })
            
            return result.data
            
        except Exception as e:
            logger.error(f"SQL reasoning generation failed: {e}")
            # 返回默认推理
            return SqlReasoningResult(
                reasoning=f"Failed to generate reasoning due to error: {str(e)}",
                step_by_step_plan=["Analyze the question", "Identify required tables", "Write SQL query"]
            )
