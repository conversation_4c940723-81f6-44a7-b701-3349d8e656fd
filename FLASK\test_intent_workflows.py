#!/usr/bin/env python3
"""
测试不同意图类型的工作流
验证SQL查询、通用问题和误导性问题的处理
"""

import requests
import time
import sys


def test_intent_workflows():
    """测试不同意图的工作流"""
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    # 测试用例：(问题, 期望意图, 期望状态)
    test_cases = [
        # SQL查询问题
        ("How many customers do we have?", "SQL_QUERY", "finished"),
        ("What is the total sales amount?", "SQL_QUERY", "finished"),
        
        # 通用问题
        ("What data is available?", "GENERAL", "finished"),
        ("How do I use this system?", "GENERAL", "finished"),
        ("What tables are in the database?", "GENERAL", "finished"),
        
        # 误导性问题
        ("Show me something", "MISLEADING_QUERY", "failed"),
        ("What about that?", "MISLEADING_QUERY", "failed"),
        ("Give me data", "MISLEADING_QUERY", "failed")
    ]
    
    print("🧪 测试不同意图类型的工作流")
    print("="*60)
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (question, expected_intent, expected_status) in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}/{total_count}: {question}")
        print(f"   期望意图: {expected_intent}")
        print(f"   期望状态: {expected_status}")
        
        try:
            # 提交问题
            ask_data = {
                "query": question,
                "project_id": "test_project",
                "configurations": {"language": "English"}
            }
            
            response = session.post(
                f"{base_url}/v1/asks",
                json=ask_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code != 200:
                print(f"   ❌ 提交失败: {response.status_code}")
                continue
            
            query_id = response.json()["query_id"]
            print(f"   ✅ 提交成功: {query_id}")
            
            # 等待处理完成
            final_status = None
            max_wait = 30  # 30秒超时
            start_time = time.time()
            
            while time.time() - start_time < max_wait:
                result_response = session.get(f"{base_url}/v1/asks/{query_id}/result")
                if result_response.status_code == 200:
                    result = result_response.json()
                    current_status = result.get("status")
                    
                    if current_status in ["finished", "failed", "stopped"]:
                        final_status = current_status
                        break
                
                time.sleep(2)
            
            if not final_status:
                print(f"   ❌ 超时：未在{max_wait}秒内完成")
                continue
            
            # 验证结果
            print(f"   📊 最终状态: {final_status}")
            
            if final_status == expected_status:
                print(f"   ✅ 状态正确")
                
                # 进一步验证响应内容
                result_type = result.get("type", "")
                print(f"   📋 响应类型: {result_type}")
                
                if expected_intent == "SQL_QUERY":
                    # SQL查询应该返回SQL
                    responses = result.get("response", [])
                    if responses and responses[0].get("sql"):
                        print(f"   ✅ 返回了SQL: {responses[0]['sql'][:50]}...")
                        success_count += 1
                    else:
                        print(f"   ❌ 未返回SQL")
                        
                elif expected_intent == "GENERAL":
                    # 通用问题应该返回GENERAL类型
                    if result_type == "GENERAL":
                        general_type = result.get("general_type", "")
                        print(f"   ✅ 通用问题类型: {general_type}")
                        success_count += 1
                    else:
                        print(f"   ❌ 期望GENERAL类型，实际: {result_type}")
                        
                elif expected_intent == "MISLEADING_QUERY":
                    # 误导性问题应该返回错误
                    error = result.get("error", {})
                    if error:
                        print(f"   ✅ 错误信息: {error.get('message', '')[:50]}...")
                        success_count += 1
                    else:
                        print(f"   ❌ 未返回错误信息")
                        
            else:
                print(f"   ❌ 状态错误：期望{expected_status}，实际{final_status}")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    print("\n" + "="*60)
    print(f"📊 测试总结: {success_count}/{total_count} 项测试通过")
    
    if success_count == total_count:
        print("🎉 所有意图工作流测试通过！")
        return True
    else:
        print("💥 部分测试失败，请检查工作流实现。")
        return False


def test_workflow_states():
    """测试工作流状态转换"""
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    print("\n🔄 测试工作流状态转换")
    print("="*60)
    
    # 提交一个SQL查询问题
    ask_data = {
        "query": "How many customers do we have?",
        "project_id": "test_project"
    }
    
    try:
        response = session.post(
            f"{base_url}/v1/asks",
            json=ask_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            print(f"❌ 提交失败: {response.status_code}")
            return False
        
        query_id = response.json()["query_id"]
        print(f"✅ 提交成功: {query_id}")
        
        # 监控状态变化
        observed_states = []
        max_wait = 30
        start_time = time.time()
        
        while time.time() - start_time < max_wait:
            result_response = session.get(f"{base_url}/v1/asks/{query_id}/result")
            if result_response.status_code == 200:
                result = result_response.json()
                current_status = result.get("status")
                
                if current_status not in observed_states:
                    observed_states.append(current_status)
                    print(f"   📍 状态变更: {current_status}")
                
                if current_status in ["finished", "failed", "stopped"]:
                    break
            
            time.sleep(1)
        
        print(f"📋 观察到的状态序列: {' → '.join(observed_states)}")
        
        # 验证状态序列
        expected_sequence = ["understanding", "searching", "generating", "validating", "finished"]
        
        # 检查是否包含推理状态（不应该包含）
        if "reasoning" in observed_states:
            print("❌ 错误：状态序列中包含推理状态")
            return False
        else:
            print("✅ 确认：状态序列中不包含推理状态")
        
        # 检查状态序列是否合理
        valid_states = ["understanding", "searching", "generating", "validating", "finished", "failed"]
        for state in observed_states:
            if state not in valid_states:
                print(f"❌ 错误：发现无效状态 {state}")
                return False
        
        print("✅ 状态序列验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 状态测试异常: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始测试意图工作流和状态转换")
    
    # 检查服务状态
    try:
        response = requests.get("http://localhost:5000/health", timeout=5)
        if response.status_code != 200:
            print("❌ 服务未运行，请先启动服务")
            sys.exit(1)
        print("✅ 服务运行正常")
    except Exception as e:
        print(f"❌ 无法连接到服务: {e}")
        sys.exit(1)
    
    # 运行测试
    test1_success = test_intent_workflows()
    test2_success = test_workflow_states()
    
    print("\n" + "="*60)
    print("📊 总体测试结果:")
    print(f"   意图工作流测试: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   状态转换测试: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过！新工作流程运行正常。")
        print("✅ 确认已成功去掉SQL推理步骤")
        print("✅ 不同意图类型的工作流都正常工作")
        sys.exit(0)
    else:
        print("\n💥 部分测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
