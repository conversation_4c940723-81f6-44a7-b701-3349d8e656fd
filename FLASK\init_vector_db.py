#!/usr/bin/env python3
"""
向量数据库初始化脚本
用于初始化 Chroma 向量数据库并添加一些示例数据
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from services.vector_db_service import VectorDBService
from config import Config


async def init_vector_db():
    """初始化向量数据库"""
    
    print("🚀 Initializing Vector Database...")
    print(f"📁 Persist Directory: {Config.CHROMA_PERSIST_DIRECTORY}")
    print(f"📚 Collection Name: {Config.CHROMA_COLLECTION_NAME}")
    print(f"🤖 Embedding Model: {Config.EMBEDDING_MODEL}")
    print(f"🎯 Similarity Threshold: {Config.SIMILARITY_THRESHOLD}")
    
    try:
        # 创建向量数据库服务
        vector_db = VectorDBService()
        
        print("\n✅ Vector database service initialized successfully")
        
        # 获取初始统计信息
        stats = await vector_db.get_collection_stats()
        print(f"📊 Initial stats: {stats.get('total_records', 0)} records")
        
        return vector_db
        
    except Exception as e:
        print(f"❌ Failed to initialize vector database: {e}")
        raise


async def add_sample_data(vector_db: VectorDBService):
    """添加示例数据"""
    
    print("\n📝 Adding sample data...")
    
    sample_data = [
        {
            "question": "How many customers do we have?",
            "sql": "SELECT COUNT(*) FROM customers",
            "project_id": "demo_project",
            "metadata": {
                "category": "count",
                "difficulty": "easy",
                "tables": ["customers"]
            }
        },
        {
            "question": "What is the total sales amount?",
            "sql": "SELECT SUM(total_amount) FROM orders",
            "project_id": "demo_project",
            "metadata": {
                "category": "aggregation",
                "difficulty": "easy",
                "tables": ["orders"]
            }
        },
        {
            "question": "Show me the top 10 customers by order value",
            "sql": "SELECT c.customer_name, SUM(o.total_amount) as total_sales FROM customers c JOIN orders o ON c.customer_id = o.customer_id GROUP BY c.customer_id, c.customer_name ORDER BY total_sales DESC LIMIT 10",
            "project_id": "demo_project",
            "metadata": {
                "category": "ranking",
                "difficulty": "medium",
                "tables": ["customers", "orders"]
            }
        },
        {
            "question": "What are the monthly sales trends?",
            "sql": "SELECT DATE_TRUNC('month', order_date) as month, SUM(total_amount) as monthly_sales FROM orders GROUP BY DATE_TRUNC('month', order_date) ORDER BY month",
            "project_id": "demo_project",
            "metadata": {
                "category": "time_series",
                "difficulty": "medium",
                "tables": ["orders"]
            }
        },
        {
            "question": "Which products are most popular?",
            "sql": "SELECT product_name, SUM(quantity) as total_quantity FROM order_items GROUP BY product_name ORDER BY total_quantity DESC LIMIT 5",
            "project_id": "demo_project",
            "metadata": {
                "category": "ranking",
                "difficulty": "easy",
                "tables": ["order_items"]
            }
        },
        {
            "question": "What is the average order value per customer?",
            "sql": "SELECT c.customer_name, AVG(o.total_amount) as avg_order_value FROM customers c JOIN orders o ON c.customer_id = o.customer_id GROUP BY c.customer_id, c.customer_name ORDER BY avg_order_value DESC",
            "project_id": "demo_project",
            "metadata": {
                "category": "aggregation",
                "difficulty": "medium",
                "tables": ["customers", "orders"]
            }
        },
        {
            "question": "Show customers who haven't placed orders recently",
            "sql": "SELECT c.customer_name, MAX(o.order_date) as last_order_date FROM customers c LEFT JOIN orders o ON c.customer_id = o.customer_id GROUP BY c.customer_id, c.customer_name HAVING MAX(o.order_date) < CURRENT_DATE - INTERVAL '90 days' OR MAX(o.order_date) IS NULL",
            "project_id": "demo_project",
            "metadata": {
                "category": "analysis",
                "difficulty": "hard",
                "tables": ["customers", "orders"]
            }
        },
        {
            "question": "What is the customer retention rate?",
            "sql": "WITH customer_orders AS (SELECT customer_id, COUNT(*) as order_count FROM orders GROUP BY customer_id) SELECT CASE WHEN order_count > 1 THEN 'Returning' ELSE 'One-time' END as customer_type, COUNT(*) as customer_count FROM customer_orders GROUP BY CASE WHEN order_count > 1 THEN 'Returning' ELSE 'One-time' END",
            "project_id": "demo_project",
            "metadata": {
                "category": "analysis",
                "difficulty": "hard",
                "tables": ["orders"]
            }
        }
    ]
    
    stored_count = 0
    
    for data in sample_data:
        try:
            doc_id = await vector_db.store_question_sql_pair(
                question=data["question"],
                sql=data["sql"],
                project_id=data["project_id"],
                metadata=data["metadata"]
            )
            
            print(f"   ✅ Stored: {data['question'][:50]}... (ID: {doc_id[:8]}...)")
            stored_count += 1
            
        except Exception as e:
            print(f"   ❌ Failed to store: {data['question'][:50]}... - {e}")
    
    print(f"\n📊 Successfully stored {stored_count}/{len(sample_data)} sample records")
    
    return stored_count


async def test_retrieval(vector_db: VectorDBService):
    """测试检索功能"""
    
    print("\n🔍 Testing retrieval functionality...")
    
    test_queries = [
        "How many customers are there?",
        "What are the total sales?",
        "Show me top customers",
        "Monthly sales analysis",
        "Popular products"
    ]
    
    for query in test_queries:
        try:
            results = await vector_db.search_similar_questions(
                question=query,
                project_id="demo_project"
            )
            
            print(f"\n   Query: '{query}'")
            if results:
                for i, result in enumerate(results[:2]):  # 只显示前2个结果
                    print(f"     {i+1}. {result['question']} (similarity: {result['similarity']})")
            else:
                print("     No similar questions found")
                
        except Exception as e:
            print(f"     ❌ Retrieval failed: {e}")


async def main():
    """主函数"""
    
    print("🔧 Vector Database Initialization Script")
    print("="*50)
    
    try:
        # 初始化向量数据库
        vector_db = await init_vector_db()
        
        # 添加示例数据
        stored_count = await add_sample_data(vector_db)
        
        # 测试检索功能
        await test_retrieval(vector_db)
        
        # 获取最终统计信息
        print("\n📊 Final Statistics:")
        stats = await vector_db.get_collection_stats()
        print(f"   Total records: {stats.get('total_records', 0)}")
        print(f"   Collection name: {stats.get('collection_name', 'N/A')}")
        print(f"   Embedding model: {stats.get('embedding_model', 'N/A')}")
        
        print("\n🎉 Vector database initialization completed successfully!")
        print(f"📁 Database location: {Config.CHROMA_PERSIST_DIRECTORY}")
        print("\n💡 You can now start the Flask service and test the historical question retrieval.")
        
    except Exception as e:
        print(f"\n💥 Initialization failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
