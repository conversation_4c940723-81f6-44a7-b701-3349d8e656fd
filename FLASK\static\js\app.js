// Wren AI Flask Service - 前端 JavaScript

class WrenAIApp {
    constructor() {
        this.baseUrl = window.location.origin;
        this.currentQueryId = null;
        this.pollInterval = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadVectorDBStats();
        this.initTabs();
    }

    bindEvents() {
        // 提交问题
        $('#askForm').on('submit', (e) => {
            e.preventDefault();
            this.submitQuestion();
        });

        // 存储问题-SQL对
        $('#storeForm').on('submit', (e) => {
            e.preventDefault();
            this.storeQuestionSQL();
        });

        // 反馈按钮
        $(document).on('click', '.feedback-btn', (e) => {
            const isHelpful = $(e.target).data('helpful');
            this.submitFeedback(isHelpful);
        });

        // 刷新统计
        $('#refreshStats').on('click', () => {
            this.loadVectorDBStats();
        });

        // 清空结果
        $('#clearResults').on('click', () => {
            this.clearResults();
        });
    }

    initTabs() {
        $('.tab').on('click', function() {
            const target = $(this).data('tab');
            
            $('.tab').removeClass('active');
            $(this).addClass('active');
            
            $('.tab-content').removeClass('active');
            $(`#${target}`).addClass('active');
        });
    }

    async submitQuestion() {
        const query = $('#userQuery').val().trim();
        const projectId = $('#projectId').val().trim();

        if (!query) {
            this.showAlert('请输入问题', 'danger');
            return;
        }

        const requestData = {
            query: query,
            project_id: projectId || null,
            configurations: {
                language: "English"
            }
        };

        try {
            this.showLoading('正在处理您的问题...');
            this.disableForm(true);

            const response = await $.ajax({
                url: `${this.baseUrl}/v1/asks`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData)
            });

            this.currentQueryId = response.query_id;
            this.showAlert(`问题已提交，查询ID: ${this.currentQueryId}`, 'info');
            
            // 开始轮询结果
            this.startPolling();

        } catch (error) {
            this.hideLoading();
            this.disableForm(false);
            this.showAlert(`提交失败: ${error.responseJSON?.error || error.statusText}`, 'danger');
        }
    }

    startPolling() {
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
        }

        this.pollInterval = setInterval(() => {
            this.checkResult();
        }, 2000);

        // 设置超时
        setTimeout(() => {
            if (this.pollInterval) {
                clearInterval(this.pollInterval);
                this.hideLoading();
                this.disableForm(false);
                this.showAlert('查询超时，请稍后查看结果', 'danger');
            }
        }, 60000); // 60秒超时
    }

    async checkResult() {
        if (!this.currentQueryId) return;

        try {
            const response = await $.ajax({
                url: `${this.baseUrl}/v1/asks/${this.currentQueryId}/result`,
                method: 'GET'
            });

            this.updateStatus(response.status);

            if (response.status === 'finished') {
                clearInterval(this.pollInterval);
                this.hideLoading();
                this.disableForm(false);
                this.displayResult(response);
            } else if (response.status === 'failed') {
                clearInterval(this.pollInterval);
                this.hideLoading();
                this.disableForm(false);
                this.displayError(response);
            }

        } catch (error) {
            console.error('轮询错误:', error);
        }
    }

    updateStatus(status) {
        const statusText = this.getStatusText(status);
        $('#currentStatus').html(`
            <span class="status-badge status-${status}">${statusText}</span>
        `);
    }

    getStatusText(status) {
        const statusMap = {
            'understanding': '理解问题',
            'searching': '检索数据',
            'reasoning': 'SQL推理',
            'generating': '生成SQL',
            'validating': '验证SQL',
            'finished': '完成',
            'failed': '失败'
        };
        return statusMap[status] || status;
    }

    displayResult(result) {
        let html = '<div class="result-container success">';
        html += '<h4>✅ 查询成功</h4>';

        if (result.response && result.response.length > 0) {
            result.response.forEach((resp, index) => {
                html += `
                    <div class="sql-result">
                        <h5>SQL ${index + 1} (${resp.type})</h5>
                        <div class="sql-code">${this.escapeHtml(resp.sql)}</div>
                        ${resp.viewId ? `<p><strong>View ID:</strong> ${resp.viewId}</p>` : ''}
                    </div>
                `;
            });

            // 添加反馈按钮
            html += `
                <div class="feedback-buttons">
                    <p><strong>这个结果对您有帮助吗？</strong></p>
                    <button class="btn btn-success feedback-btn" data-helpful="true">
                        👍 有帮助
                    </button>
                    <button class="btn btn-danger feedback-btn" data-helpful="false">
                        👎 没帮助
                    </button>
                </div>
            `;
        }

        if (result.rephrased_question) {
            html += `<p><strong>重新表述的问题:</strong> ${this.escapeHtml(result.rephrased_question)}</p>`;
        }

        if (result.intent_reasoning) {
            html += `<p><strong>意图分析:</strong> ${this.escapeHtml(result.intent_reasoning)}</p>`;
        }

        if (result.sql_generation_reasoning) {
            html += `<p><strong>SQL生成推理:</strong> ${this.escapeHtml(result.sql_generation_reasoning)}</p>`;
        }

        html += '</div>';
        $('#queryResult').html(html);
    }

    displayError(result) {
        let html = '<div class="result-container error">';
        html += '<h4>❌ 查询失败</h4>';

        if (result.error) {
            html += `
                <p><strong>错误代码:</strong> ${result.error.code}</p>
                <p><strong>错误信息:</strong> ${this.escapeHtml(result.error.message)}</p>
            `;
        }

        if (result.invalid_sql) {
            html += `
                <h5>无效的SQL:</h5>
                <div class="sql-code">${this.escapeHtml(result.invalid_sql)}</div>
            `;
        }

        html += '</div>';
        $('#queryResult').html(html);
    }

    async submitFeedback(isHelpful) {
        if (!this.currentQueryId) {
            this.showAlert('没有可反馈的查询', 'danger');
            return;
        }

        try {
            const response = await $.ajax({
                url: `${this.baseUrl}/v1/asks/${this.currentQueryId}/feedback`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    is_helpful: isHelpful,
                    comment: isHelpful ? "用户认为有帮助" : "用户认为没帮助"
                })
            });

            if (response.success) {
                this.showAlert('反馈提交成功，谢谢您的反馈！', 'success');
                $('.feedback-buttons').html('<p class="text-success">✅ 反馈已提交</p>');
                
                // 刷新统计信息
                setTimeout(() => {
                    this.loadVectorDBStats();
                }, 1000);
            } else {
                this.showAlert('反馈提交失败', 'danger');
            }

        } catch (error) {
            this.showAlert(`反馈提交失败: ${error.responseJSON?.error || error.statusText}`, 'danger');
        }
    }

    async storeQuestionSQL() {
        const question = $('#storeQuestion').val().trim();
        const sql = $('#storeSQL').val().trim();
        const projectId = $('#storeProjectId').val().trim();
        const viewId = $('#storeViewId').val().trim();

        if (!question || !sql) {
            this.showAlert('请填写问题和SQL', 'danger');
            return;
        }

        const requestData = {
            question: question,
            sql: sql,
            project_id: projectId || null,
            view_id: viewId || null,
            metadata: {
                manual_store: true,
                timestamp: new Date().toISOString()
            }
        };

        try {
            $('#storeBtn').prop('disabled', true).text('存储中...');

            const response = await $.ajax({
                url: `${this.baseUrl}/v1/vector-db/store`,
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(requestData)
            });

            if (response.success) {
                this.showAlert(`存储成功！文档ID: ${response.doc_id}`, 'success');
                $('#storeForm')[0].reset();
                
                // 刷新统计信息
                setTimeout(() => {
                    this.loadVectorDBStats();
                }, 1000);
            } else {
                this.showAlert('存储失败', 'danger');
            }

        } catch (error) {
            this.showAlert(`存储失败: ${error.responseJSON?.error || error.statusText}`, 'danger');
        } finally {
            $('#storeBtn').prop('disabled', false).text('存储到向量数据库');
        }
    }

    async loadVectorDBStats() {
        try {
            const response = await $.ajax({
                url: `${this.baseUrl}/v1/vector-db/stats`,
                method: 'GET'
            });

            this.displayStats(response);

        } catch (error) {
            console.error('加载统计信息失败:', error);
            $('#vectorDBStats').html('<p class="text-danger">加载统计信息失败</p>');
        }
    }

    displayStats(stats) {
        const html = `
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">${stats.total_records || 0}</div>
                    <div class="stat-label">总记录数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${stats.similarity_threshold || 0}</div>
                    <div class="stat-label">相似度阈值</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${(stats.avg_feedback_per_record || 0).toFixed(1)}</div>
                    <div class="stat-label">平均反馈数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${((stats.helpful_rate || 0) * 100).toFixed(1)}%</div>
                    <div class="stat-label">有用率</div>
                </div>
            </div>
            <div style="margin-top: 20px;">
                <p><strong>集合名称:</strong> ${stats.collection_name || 'N/A'}</p>
                <p><strong>嵌入模型:</strong> ${stats.embedding_model || 'N/A'}</p>
            </div>
        `;
        $('#vectorDBStats').html(html);
    }

    showLoading(message = '处理中...') {
        $('#loadingMessage').text(message);
        $('#loading').show();
    }

    hideLoading() {
        $('#loading').hide();
    }

    disableForm(disabled) {
        $('#askForm input, #askForm textarea, #askForm button').prop('disabled', disabled);
    }

    showAlert(message, type = 'info') {
        const alertHtml = `
            <div class="alert alert-${type}" role="alert">
                ${this.escapeHtml(message)}
            </div>
        `;
        $('#alerts').html(alertHtml);
        
        // 自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                $('#alerts').empty();
            }, 5000);
        }
    }

    clearResults() {
        $('#queryResult').empty();
        $('#currentStatus').empty();
        $('#alerts').empty();
        this.currentQueryId = null;
        
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// 初始化应用
$(document).ready(() => {
    window.wrenApp = new WrenAIApp();
});
