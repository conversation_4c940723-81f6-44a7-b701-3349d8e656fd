import json
import logging
from typing import List, Optional
from pydantic_ai import Agent
from .base_agent import BaseAgent
from models.data_models import IntentResult, AskHistory, Configuration

logger = logging.getLogger(__name__)


class IntentAgent(BaseAgent):
    """意图分类 Agent"""
    
    def __init__(self):
        super().__init__()
        self.agent = self.create_agent(
            system_prompt=self._get_system_prompt(),
            result_type=IntentResult
        )
    
    def _get_system_prompt(self) -> str:
        return """
### TASK ###
You are a helpful data analyst who is great at understanding user's question and classifying the intent.

Your task is to classify the user's question into one of the following categories:
1. SQL_QUERY: The user wants to query data from the database
2. GENERAL: The user is asking general questions about data or needs assistance
3. MISLEADING_QUERY: The user's question is unclear, ambiguous, or cannot be answered with the available data

### CLASSIFICATION RULES ###
- If the user is asking for specific data, metrics, or wants to analyze data, classify as SQL_QUERY
- If the user is asking general questions about the data structure, definitions, or needs help understanding the data, classify as GENERAL
- If the question is too vague, contains unclear references, or asks for data that doesn't exist, classify as MISLEADING_QUERY

### OUTPUT FORMAT ###
You must respond with a JSON object containing:
- intent: one of "SQL_QUERY", "GENERAL", "MISLEADING_QUERY"
- reasoning: explanation for the classification
- rephrased_question: a clearer version of the question (optional)
- confidence: confidence score between 0.0 and 1.0
"""
    
    async def classify_intent(
        self,
        query: str,
        histories: List[AskHistory] = None,
        sql_samples: List[dict] = None,
        instructions: List[dict] = None,
        project_id: Optional[str] = None,
        configuration: Configuration = None
    ) -> IntentResult:
        """分类用户意图"""
        
        try:
            self.log_agent_action("classify_intent", {
                "query": query,
                "project_id": project_id,
                "has_histories": bool(histories),
                "has_sql_samples": bool(sql_samples)
            })
            
            # 构建上下文信息
            context_parts = [f"User Question: {query}"]
            
            if histories:
                context_parts.append("Previous Questions:")
                for i, history in enumerate(histories[-3:]):  # 只取最近3个历史
                    context_parts.append(f"{i+1}. {history.question}")
            
            if sql_samples:
                context_parts.append("Available SQL Examples:")
                for i, sample in enumerate(sql_samples[:3]):  # 只取前3个样例
                    if isinstance(sample, dict) and 'question' in sample:
                        context_parts.append(f"{i+1}. {sample['question']}")
            
            if instructions:
                context_parts.append("Available Instructions:")
                for i, instruction in enumerate(instructions[:3]):
                    if isinstance(instruction, dict) and 'instruction' in instruction:
                        context_parts.append(f"{i+1}. {instruction['instruction']}")
            
            prompt = "\n\n".join(context_parts)
            
            # 使用 Agent 进行分类
            result = await self.agent.run(prompt)
            
            self.log_agent_action("intent_classified", {
                "intent": result.data.intent,
                "confidence": result.data.confidence,
                "reasoning": result.data.reasoning[:100] + "..." if len(result.data.reasoning) > 100 else result.data.reasoning
            })
            
            return result.data
            
        except Exception as e:
            logger.error(f"Intent classification failed: {e}")
            # 返回默认结果
            return IntentResult(
                intent="GENERAL",
                reasoning=f"Classification failed due to error: {str(e)}",
                confidence=0.0
            )
