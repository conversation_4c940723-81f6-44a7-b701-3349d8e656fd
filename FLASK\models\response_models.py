from typing import Optional, List, Literal
from pydantic import BaseModel


class AskResponse(BaseModel):
    query_id: str


class AskResult(BaseModel):
    sql: str
    type: Literal["llm", "view"] = "llm"
    viewId: Optional[str] = None


class AskError(BaseModel):
    code: Literal["NO_RELEVANT_DATA", "NO_RELEVANT_SQL", "OTHERS"]
    message: str


class AskResultResponse(BaseModel):
    status: Literal["understanding", "searching", "reasoning", "generating", "validating", "finished", "failed", "stopped"]
    response: List[AskResult] = []
    error: Optional[AskError] = None
    trace_id: Optional[str] = None
    is_followup: bool = False
    rephrased_question: Optional[str] = None
    intent_reasoning: Optional[str] = None
    sql_generation_reasoning: Optional[str] = None
    type: Optional[Literal["TEXT_TO_SQL", "GENERAL"]] = None
    retrieved_tables: Optional[List[str]] = None
    invalid_sql: Optional[str] = None
    general_type: Optional[Literal["MISLEADING_QUERY", "DATA_ASSISTANCE", "USER_GUIDE"]] = None


class StopAskResponse(BaseModel):
    query_id: str


class FeedbackResponse(BaseModel):
    success: bool
    message: str


class StoreQuestionSqlResponse(BaseModel):
    success: bool
    doc_id: Optional[str] = None
    message: str


class VectorDBStatsResponse(BaseModel):
    total_records: int
    collection_name: str
    embedding_model: str
    similarity_threshold: float
    avg_feedback_per_record: Optional[float] = None
    helpful_rate: Optional[float] = None
