# Wren AI Flask Service 部署指南

## 系统架构概述

新系统基于 PydanticAI Agent Workflow 架构，包含以下核心组件：

### Agent 工作流

1. **HistoricalQuestionAgent** - 历史问题检索
2. **SqlPairsRetrievalAgent** - SQL 样例检索  
3. **InstructionsRetrievalAgent** - 指令检索
4. **SqlFunctionsAgent** - SQL 函数检索
5. **IntentAgent** - 意图分类
6. **SchemaRetrievalAgent** - 表结构检索
7. **SqlReasoningAgent** - SQL 推理
8. **SqlGenerationAgent** - SQL 生成
9. **SqlValidationAgent** - SQL 验证

### 工作流程

```
用户问题 → 历史问题检索 → [找到历史问题] → 直接返回
                    ↓ [未找到]
                并行检索(SQL样例/指令/函数)
                    ↓
                意图分类
                    ↓
        [SQL_QUERY] → 表结构检索 → SQL推理 → SQL生成 → SQL验证
        [GENERAL] → 通用回答
        [MISLEADING] → 错误提示
```

### 错误处理和重试机制

- SQL 生成失败 → 反馈给 SQL 推理 Agent 重新推理
- SQL 验证失败 → 反馈给 SQL 生成 Agent 重新生成
- 最大重试次数：3次（可配置）

## 部署步骤

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
cd F:\buster\wren-ai-service\FLASK
pip install -r requirements.txt
```

### 2. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件
nano .env
```

必需配置：
```env
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat
```

可选配置：
```env
DEBUG=True
MAX_SQL_CORRECTION_RETRIES=3
ENGINE_TIMEOUT=30.0
CACHE_TTL=120
WREN_ENGINE_ENDPOINT=http://localhost:8080
```

### 3. 启动服务

#### 方式一：直接启动
```bash
python start.py
```

#### 方式二：使用 Docker
```bash
# 构建镜像
docker build -t wren-ai-flask .

# 运行容器
docker run -p 5000:5000 \
  -e DEEPSEEK_API_KEY=your_key \
  -e DEBUG=False \
  wren-ai-flask
```

#### 方式三：使用 Docker Compose
```bash
# 设置环境变量
export DEEPSEEK_API_KEY=your_key

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 4. 验证部署

```bash
# 健康检查
curl http://localhost:5000/health

# 运行测试
python test_api.py

# 运行使用示例
python example_usage.py
```

## API 使用指南

### 基本用法

```python
import requests

# 1. 提交问题
response = requests.post('http://localhost:5000/v1/asks', json={
    "query": "How many customers do we have?",
    "project_id": "my_project",
    "configurations": {
        "language": "English"
    }
})
query_id = response.json()["query_id"]

# 2. 获取结果
result = requests.get(f'http://localhost:5000/v1/asks/{query_id}/result')
print(result.json())
```

### 高级用法

```python
# 带历史记录的查询
response = requests.post('http://localhost:5000/v1/asks', json={
    "query": "What about the sales trend?",
    "project_id": "my_project",
    "histories": [
        {
            "sql": "SELECT SUM(total_amount) FROM orders",
            "question": "What is the total sales?"
        }
    ],
    "configurations": {
        "language": "English",
        "fiscal_year": {"start": "2023-01-01", "end": "2023-12-31"}
    },
    "ignore_sql_generation_reasoning": False,
    "enable_column_pruning": True
})
```

## 监控和维护

### 日志监控

```bash
# 查看实时日志
tail -f wren-ai-flask.log

# 查看错误日志
grep ERROR wren-ai-flask.log

# 查看特定查询的日志
grep "query_id_here" wren-ai-flask.log
```

### 性能监控

```bash
# 检查内存使用
ps aux | grep python

# 检查端口占用
netstat -tlnp | grep 5000

# 检查 API 响应时间
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:5000/health
```

### 缓存管理

系统使用 TTL 缓存，默认配置：
- 缓存大小：1,000,000 条记录
- 缓存时间：120 秒

可通过环境变量调整：
```env
CACHE_MAXSIZE=2000000
CACHE_TTL=300
```

### 故障排除

#### 常见问题

1. **DeepSeek API 调用失败**
   ```bash
   # 检查 API 密钥
   curl -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
        https://api.deepseek.com/v1/models
   ```

2. **内存不足**
   ```bash
   # 减少缓存大小
   export CACHE_MAXSIZE=100000
   ```

3. **SQL 验证失败**
   ```bash
   # 检查 Wren Engine 连接
   curl http://localhost:8080/health
   ```

#### 调试模式

开启调试模式获取详细信息：
```env
DEBUG=True
```

调试端点：
```bash
# 获取工作流详细信息
curl http://localhost:5000/v1/debug/workflow/{query_id}
```

## 生产环境配置

### 安全配置

```env
DEBUG=False
SECRET_KEY=your-production-secret-key
```

### 反向代理配置 (Nginx)

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 进程管理 (Systemd)

```ini
[Unit]
Description=Wren AI Flask Service
After=network.target

[Service]
Type=simple
User=wren
WorkingDirectory=/path/to/wren-ai-service/FLASK
Environment=PATH=/path/to/venv/bin
ExecStart=/path/to/venv/bin/python start.py
Restart=always

[Install]
WantedBy=multi-user.target
```

### 日志轮转

```bash
# /etc/logrotate.d/wren-ai-flask
/path/to/wren-ai-service/FLASK/wren-ai-flask.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 wren wren
    postrotate
        systemctl reload wren-ai-flask
    endscript
}
```

## 扩展和定制

### 添加新的 Agent

1. 创建新的 Agent 类继承 `BaseAgent`
2. 在 `agents/__init__.py` 中导入
3. 在 `WorkflowService` 中集成
4. 更新工作流逻辑

### 修改工作流

在 `services/workflow_service.py` 中的 `_execute_workflow` 方法中修改流程逻辑。

### 集成外部服务

通过修改相应的 Agent 来集成：
- 向量数据库（用于检索）
- 外部 SQL 引擎
- 监控系统
- 日志聚合服务
