#!/usr/bin/env python3
"""
Wren AI Flask Service 使用示例
"""

import requests
import json
import time


def example_usage():
    """示例用法"""
    base_url = "http://localhost:5000"
    
    # 1. 检查服务健康状态
    print("1. 检查服务健康状态...")
    health_response = requests.get(f"{base_url}/health")
    print(f"   状态: {health_response.status_code}")
    print(f"   响应: {health_response.json()}")
    
    # 2. 提交问题
    print("\n2. 提交问题...")
    ask_data = {
        "query": "How many customers do we have in total?",
        "project_id": "demo_project",
        "configurations": {
            "language": "English"
        },
        "histories": [
            {
                "sql": "SELECT COUNT(*) FROM orders",
                "question": "How many orders do we have?"
            }
        ]
    }
    
    ask_response = requests.post(
        f"{base_url}/v1/asks",
        json=ask_data,
        headers={"Content-Type": "application/json"}
    )
    
    if ask_response.status_code != 200:
        print(f"   错误: {ask_response.status_code} - {ask_response.text}")
        return
    
    query_id = ask_response.json()["query_id"]
    print(f"   查询ID: {query_id}")
    
    # 3. 轮询结果
    print("\n3. 等待处理结果...")
    max_wait = 60
    poll_interval = 3
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        # 检查状态
        status_response = requests.get(f"{base_url}/v1/asks/{query_id}/status")
        if status_response.status_code == 200:
            status_data = status_response.json()
            status = status_data["status"]
            print(f"   当前状态: {status}")
            
            if status in ["finished", "failed", "stopped"]:
                break
        
        time.sleep(poll_interval)
    
    # 4. 获取最终结果
    print("\n4. 获取最终结果...")
    result_response = requests.get(f"{base_url}/v1/asks/{query_id}/result")
    
    if result_response.status_code == 200:
        result_data = result_response.json()
        print(f"   状态: {result_data['status']}")
        
        if result_data.get('error'):
            error = result_data['error']
            print(f"   错误: {error['code']} - {error['message']}")
        
        if result_data.get('response'):
            for i, response in enumerate(result_data['response']):
                print(f"   SQL {i+1}: {response['sql']}")
                print(f"   类型: {response['type']}")
        
        if result_data.get('rephrased_question'):
            print(f"   重新表述的问题: {result_data['rephrased_question']}")
        
        if result_data.get('intent_reasoning'):
            print(f"   意图推理: {result_data['intent_reasoning']}")
        
        if result_data.get('sql_generation_reasoning'):
            print(f"   SQL生成推理: {result_data['sql_generation_reasoning']}")
    
    else:
        print(f"   获取结果失败: {result_response.status_code}")
    
    # 5. 获取调试信息（如果是开发模式）
    print("\n5. 获取调试信息...")
    debug_response = requests.get(f"{base_url}/v1/debug/workflow/{query_id}")
    
    if debug_response.status_code == 200:
        debug_data = debug_response.json()
        print(f"   工作流状态: {debug_data['status']}")
        print(f"   重试次数: {debug_data['retry_count']}")
        if debug_data.get('error_messages'):
            print(f"   错误信息: {debug_data['error_messages']}")
    elif debug_response.status_code == 403:
        print("   调试端点在生产模式下不可用")
    else:
        print(f"   获取调试信息失败: {debug_response.status_code}")


def test_different_queries():
    """测试不同类型的查询"""
    base_url = "http://localhost:5000"
    
    test_queries = [
        {
            "name": "SQL查询 - 客户数量",
            "query": "How many customers do we have?",
            "expected_intent": "SQL_QUERY"
        },
        {
            "name": "SQL查询 - 销售总额",
            "query": "What is the total sales amount for this year?",
            "expected_intent": "SQL_QUERY"
        },
        {
            "name": "通用问题 - 数据说明",
            "query": "What kind of data do you have?",
            "expected_intent": "GENERAL"
        },
        {
            "name": "误导性问题",
            "query": "Tell me about the weather tomorrow",
            "expected_intent": "MISLEADING_QUERY"
        }
    ]
    
    print("测试不同类型的查询...\n")
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"{i}. {test_case['name']}")
        print(f"   查询: {test_case['query']}")
        
        # 提交查询
        ask_data = {
            "query": test_case["query"],
            "project_id": "test_project",
            "configurations": {"language": "English"}
        }
        
        ask_response = requests.post(
            f"{base_url}/v1/asks",
            json=ask_data,
            headers={"Content-Type": "application/json"}
        )
        
        if ask_response.status_code == 200:
            query_id = ask_response.json()["query_id"]
            print(f"   查询ID: {query_id}")
            
            # 等待结果
            time.sleep(5)
            
            # 获取结果
            result_response = requests.get(f"{base_url}/v1/asks/{query_id}/result")
            if result_response.status_code == 200:
                result_data = result_response.json()
                print(f"   状态: {result_data['status']}")
                
                if result_data.get('response'):
                    print(f"   生成了 {len(result_data['response'])} 个SQL")
                
                if result_data.get('error'):
                    print(f"   错误: {result_data['error']['code']}")
            
        print()


if __name__ == "__main__":
    print("Wren AI Flask Service 使用示例\n")
    print("请确保服务已启动在 http://localhost:5000\n")
    
    try:
        # 基本使用示例
        example_usage()
        
        print("\n" + "="*50)
        
        # 测试不同查询类型
        test_different_queries()
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务，请确保服务已启动")
    except Exception as e:
        print(f"❌ 发生错误: {e}")
