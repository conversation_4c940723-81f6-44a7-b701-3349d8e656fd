#!/usr/bin/env python3
"""
测试去掉推理步骤后的工作流
验证新的工作流程是否正常运行
"""

import requests
import json
import time
import sys


class WorkflowTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()

    def test_service_status(self):
        """测试服务状态"""
        print("🔍 检查服务状态...")
        try:
            response = self.session.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ 服务运行正常")
                return True
            else:
                print(f"❌ 服务响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到服务: {e}")
            return False

    def test_workflow_without_reasoning(self):
        """测试去掉推理步骤的工作流"""
        print("\n🤖 测试新工作流程...")

        test_questions = [
            # SQL查询问题
            ("How many customers do we have?", "SQL_QUERY"),
            ("What is the total sales amount?", "SQL_QUERY"),
            ("Show me the top 5 customers by order value", "SQL_QUERY"),

            # 通用问题
            ("What data is available in the database?", "GENERAL"),
            ("How do I use this system?", "GENERAL"),

            # 误导性问题
            ("Show me something interesting", "MISLEADING_QUERY"),
            ("What about that thing?", "MISLEADING_QUERY")
        ]

        for i, (question, expected_intent) in enumerate(test_questions, 1):
            print(f"\n--- 测试问题 {i}: {question} (期望意图: {expected_intent}) ---")

            # 提交问题
            ask_data = {
                "query": question,
                "project_id": "test_project",
                "configurations": {
                    "language": "English"
                }
            }

            try:
                response = self.session.post(
                    f"{self.base_url}/v1/asks",
                    json=ask_data,
                    headers={"Content-Type": "application/json"}
                )

                if response.status_code == 200:
                    query_id = response.json()["query_id"]
                    print(f"✅ 问题提交成功，查询ID: {query_id}")

                    # 监控工作流状态
                    self.monitor_workflow_states(query_id, expected_intent)

                    # 获取最终结果
                    final_result = self.get_final_result(query_id)
                    if final_result:
                        self.analyze_workflow_result(final_result, expected_intent)

                else:
                    print(f"❌ 问题提交失败: {response.status_code}")
                    print(f"   响应: {response.text}")

            except Exception as e:
                print(f"❌ 测试异常: {e}")

    def monitor_workflow_states(self, query_id, expected_intent):
        """监控工作流状态变化"""
        print("📊 监控工作流状态...")

        # 根据意图类型确定期望的状态序列
        if expected_intent == "SQL_QUERY":
            expected_states = ["understanding", "searching", "generating", "validating", "finished"]
        else:  # GENERAL 或 MISLEADING_QUERY
            expected_states = ["understanding", "finished"]  # 或 "failed" for MISLEADING_QUERY

        observed_states = []

        max_wait_time = 60  # 最大等待60秒
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                response = self.session.get(f"{self.base_url}/v1/asks/{query_id}/result")
                if response.status_code == 200:
                    result = response.json()
                    current_status = result.get("status")

                    if current_status not in observed_states:
                        observed_states.append(current_status)
                        print(f"   状态变更: {current_status}")

                    if current_status in ["finished", "failed", "stopped"]:
                        break

                time.sleep(2)

            except Exception as e:
                print(f"   监控异常: {e}")
                break

        # 验证状态序列
        print(f"   观察到的状态序列: {observed_states}")

        # 检查是否包含推理状态（应该不包含）
        if "reasoning" in observed_states:
            print("❌ 错误：工作流中仍包含推理状态")
            return False
        else:
            print("✅ 确认：工作流中已去掉推理状态")

        # 检查状态序列是否合理
        valid_sequence = self.validate_state_sequence(observed_states)
        if valid_sequence:
            print("✅ 状态序列正常")
        else:
            print("❌ 状态序列异常")

        return valid_sequence

    def validate_state_sequence(self, states):
        """验证状态序列是否合理"""
        # 期望的状态转换规则
        valid_transitions = {
            "understanding": ["searching", "finished", "failed"],
            "searching": ["generating", "failed"],
            "generating": ["validating", "failed"],
            "validating": ["finished", "generating", "failed"],  # 可以重试回到generating
            "finished": [],
            "failed": []
        }

        for i in range(len(states) - 1):
            current_state = states[i]
            next_state = states[i + 1]

            if next_state not in valid_transitions.get(current_state, []):
                print(f"   ❌ 无效状态转换: {current_state} → {next_state}")
                return False

        return True

    def get_final_result(self, query_id):
        """获取最终结果"""
        try:
            response = self.session.get(f"{self.base_url}/v1/asks/{query_id}/result")
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ 获取结果失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 获取结果异常: {e}")
            return None

    def analyze_workflow_result(self, result, expected_intent):
        """分析工作流结果"""
        print("📋 分析工作流结果...")

        status = result.get("status")
        result_type = result.get("type", "")
        print(f"   最终状态: {status}")
        print(f"   响应类型: {result_type}")

        if status == "finished":
            if expected_intent == "SQL_QUERY":
                # SQL查询问题应该返回SQL
                responses = result.get("response", [])
                if responses:
                    for i, resp in enumerate(responses):
                        sql = resp.get("sql", "")
                        resp_type = resp.get("type", "")
                        print(f"   SQL {i+1}: {sql[:100]}...")
                        print(f"   类型: {resp_type}")

                    # 检查是否有推理相关的字段（应该没有）
                    if "sql_generation_reasoning" in result:
                        print("❌ 错误：响应中仍包含SQL推理字段")
                    else:
                        print("✅ 确认：响应中已去掉SQL推理字段")

                    if result.get("retrieved_tables"):
                        print(f"   检索到的表: {result['retrieved_tables']}")
                else:
                    print("❌ 错误：SQL查询问题没有返回SQL结果")

            elif expected_intent == "GENERAL":
                # 通用问题应该返回通用类型
                general_type = result.get("general_type")
                print(f"   通用问题类型: {general_type}")
                if result_type != "GENERAL":
                    print(f"❌ 错误：期望GENERAL类型，实际得到{result_type}")
                else:
                    print("✅ 确认：正确识别为通用问题")

            # 检查其他字段
            if result.get("intent_reasoning"):
                print(f"   意图推理: {result['intent_reasoning'][:100]}...")

        elif status == "failed":
            error = result.get("error", {})
            print(f"   错误代码: {error.get('code')}")
            print(f"   错误信息: {error.get('message')}")

            if expected_intent == "MISLEADING_QUERY":
                print("✅ 确认：误导性问题正确返回失败状态")
            elif expected_intent in ["SQL_QUERY", "GENERAL"]:
                print("❌ 警告：正常问题返回了失败状态")

            if result.get("invalid_sql"):
                print(f"   无效SQL: {result['invalid_sql'][:100]}...")

            general_type = result.get("general_type")
            if general_type:
                print(f"   通用问题类型: {general_type}")

    def test_vector_db_integration(self):
        """测试向量数据库集成"""
        print("\n📊 测试向量数据库集成...")

        try:
            # 获取统计信息
            response = self.session.get(f"{self.base_url}/v1/vector-db/stats")
            if response.status_code == 200:
                stats = response.json()
                print(f"✅ 向量数据库统计:")
                print(f"   总记录数: {stats.get('total_records', 0)}")
                print(f"   集合名称: {stats.get('collection_name', 'N/A')}")
                print(f"   相似度阈值: {stats.get('similarity_threshold', 0)}")
                return True
            else:
                print(f"❌ 获取统计失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 向量数据库测试异常: {e}")
            return False

    def test_feedback_mechanism(self):
        """测试反馈机制"""
        print("\n👍 测试反馈机制...")

        # 先提交一个简单问题
        ask_data = {
            "query": "How many customers do we have?",
            "project_id": "test_project"
        }

        try:
            response = self.session.post(
                f"{self.base_url}/v1/asks",
                json=ask_data,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code == 200:
                query_id = response.json()["query_id"]

                # 等待处理完成
                time.sleep(10)

                # 提交正面反馈
                feedback_data = {
                    "is_helpful": True,
                    "comment": "测试反馈"
                }

                feedback_response = self.session.post(
                    f"{self.base_url}/v1/asks/{query_id}/feedback",
                    json=feedback_data,
                    headers={"Content-Type": "application/json"}
                )

                if feedback_response.status_code == 200:
                    feedback_result = feedback_response.json()
                    if feedback_result.get("success"):
                        print("✅ 反馈提交成功")
                        return True
                    else:
                        print("❌ 反馈提交失败")
                        return False
                else:
                    print(f"❌ 反馈请求失败: {feedback_response.status_code}")
                    return False
            else:
                print(f"❌ 问题提交失败: {response.status_code}")
                return False

        except Exception as e:
            print(f"❌ 反馈测试异常: {e}")
            return False

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始测试去掉推理步骤的工作流\n")

        tests = [
            ("服务状态", self.test_service_status),
            ("新工作流程", self.test_workflow_without_reasoning),
            ("向量数据库集成", self.test_vector_db_integration),
            ("反馈机制", self.test_feedback_mechanism)
        ]

        success_count = 0

        for test_name, test_func in tests:
            print(f"\n{'='*50}")
            print(f"测试: {test_name}")
            print('='*50)

            try:
                if test_func():
                    success_count += 1
                    print(f"✅ {test_name} 测试通过")
                else:
                    print(f"❌ {test_name} 测试失败")
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")

        print(f"\n{'='*50}")
        print(f"📊 测试总结: {success_count}/{len(tests)} 项测试通过")

        if success_count == len(tests):
            print("🎉 所有测试通过！新工作流程运行正常。")
        else:
            print("💥 部分测试失败，请检查系统配置。")

        return success_count == len(tests)


def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"

    print(f"测试服务地址: {base_url}")
    print("测试目标: 验证去掉推理步骤后的工作流")

    tester = WorkflowTester(base_url)
    success = tester.run_all_tests()

    if success:
        print("\n🎉 新工作流程测试完全通过！")
        print("✅ 确认已成功去掉SQL推理步骤")
        print("✅ 工作流程: 表结构检索 → SQL生成 → SQL验证")
        sys.exit(0)
    else:
        print("\n💥 新工作流程测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
