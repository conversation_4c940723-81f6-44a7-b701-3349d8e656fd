[tool.poetry]
name = "wren-ai-service"
version = "0.22.10"
description = ""
authors = ["<EMAIL>"]
license = "AGPL-3.0"
readme = "README.md"
package-mode = false

[tool.poetry.dependencies]
python = ">=3.12.*, <3.13"
fastapi = "^0.115.2"
uvicorn = {extras = ["standard"], version = "^0.30.1"}
python-dotenv = "^1.0.1"
haystack-ai = "==2.7.0"
openai = "^1.40.0"
qdrant-haystack = "^7.0.0"
backoff = "^2.2.1"
tqdm = "^4.66.4"
numpy = "^1.26.4"
sqlparse = "^0.5.0"
orjson = "^3.10.3"
sf-hamilton = {version = "^1.69.0"}
aiohttp = {extras = ["speedups"], version = "^3.10.2"}
ollama-haystack = "^0.0.6"
langfuse = "^2.43.3"
ollama = "^0.2.1"
toml = "^0.10.2"
sqlglot = "^25.18.0"
cachetools = "^5.5.0"
pyyaml = "^6.0.2"
pydantic-settings = "^2.5.2"
google-auth = "^2.35.0"
tiktoken = "^0.8.0"
jsonschema = "^4.23.0"
litellm = "^1.65.1"
boto3 = "^1.35.90"
qdrant-client = "==1.11.0"

[tool.poetry.group.dev.dependencies]
pre-commit = "^3.7.1"
streamlit = "^1.37.0"
watchdog = "^4.0.0"
pandas = "^2.2.2"
matplotlib = "^3.9.2"
sseclient-py = "^1.8.0"
dspy-ai = "^2.5.26"
requests = "^2.32.2"
extra-streamlit-components = "^0.1.71"
deepeval = "^1.0.6"
tomlkit = "^0.13.0"
nltk = "^3.9.1"

[tool.poetry.group.eval.dependencies]
gitpython = "^3.1.43"
plotly = "^5.24.1"
nbformat = "^5.1.3"
ipykernel = "^6.29.5"
itables = "^2.2.1"
gdown = "^5.2.0"
streamlit-tags = "^1.2.8"

[tool.poetry.group.test.dependencies]
locust = "^2.32.0"
pytest = "^8.3.0"
pytest-cov = "^6.0.0"
pytest-asyncio = "^0.24.0"
aioresponses = "^0.7.0"
pytest-mock = "^3.14.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
