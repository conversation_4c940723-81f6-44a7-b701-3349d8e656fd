#!/usr/bin/env python3
"""
前端功能演示脚本
自动打开浏览器并展示前端功能
"""

import webbrowser
import time
import requests
import sys
from pathlib import Path


def check_service_status(base_url="http://localhost:5000"):
    """检查服务状态"""
    print("🔍 检查服务状态...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务运行正常")
            return True
        else:
            print(f"❌ 服务响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务")
        print("💡 请先启动服务: python start.py")
        return False
    except Exception as e:
        print(f"❌ 检查服务时出错: {e}")
        return False


def check_vector_db_status(base_url="http://localhost:5000"):
    """检查向量数据库状态"""
    print("📊 检查向量数据库状态...")
    try:
        response = requests.get(f"{base_url}/v1/vector-db/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"✅ 向量数据库正常，共有 {stats.get('total_records', 0)} 条记录")
            return True
        else:
            print(f"❌ 向量数据库响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 检查向量数据库时出错: {e}")
        return False


def open_frontend_pages(base_url="http://localhost:5000"):
    """打开前端页面"""
    print("🌐 打开前端页面...")
    
    pages = [
        (f"{base_url}/demo", "演示页面"),
        (f"{base_url}", "主页面")
    ]
    
    for url, name in pages:
        try:
            print(f"   📱 打开{name}: {url}")
            webbrowser.open(url)
            time.sleep(2)  # 等待浏览器打开
        except Exception as e:
            print(f"   ❌ 打开{name}失败: {e}")


def show_usage_instructions():
    """显示使用说明"""
    print("\n" + "="*60)
    print("📖 前端使用说明")
    print("="*60)
    
    print("\n🎯 演示页面功能:")
    print("  • 点击预设问题按钮快速体验")
    print("  • 观察实时处理状态")
    print("  • 查看生成的SQL结果")
    
    print("\n💬 主页面功能:")
    print("  • 智能问答 - 输入自然语言问题获取SQL")
    print("  • 数据管理 - 手动添加问题-SQL对")
    print("  • 统计信息 - 查看系统使用情况")
    
    print("\n🔄 完整工作流程:")
    print("  1. 输入问题 (例如: How many customers do we have?)")
    print("  2. 系统检索历史问题")
    print("  3. 如果没找到，执行AI生成流程")
    print("  4. 返回SQL结果")
    print("  5. 提供反馈 (👍 有帮助 / 👎 没帮助)")
    print("  6. 系统自动学习和优化")
    
    print("\n💡 示例问题:")
    example_questions = [
        "How many customers do we have?",
        "What is the total sales amount?",
        "Show me the top 5 customers by order value",
        "What are the monthly sales trends?",
        "Which products are most popular?"
    ]
    
    for i, question in enumerate(example_questions, 1):
        print(f"  {i}. {question}")
    
    print("\n🎨 界面特性:")
    print("  • 响应式设计 - 支持桌面、平板、手机")
    print("  • 实时状态 - 显示处理进度")
    print("  • 智能反馈 - 持续学习优化")
    print("  • 数据可视化 - 统计图表展示")
    
    print("\n" + "="*60)


def run_demo_workflow(base_url="http://localhost:5000"):
    """运行演示工作流"""
    print("\n🚀 启动前端演示...")
    
    # 1. 检查服务状态
    if not check_service_status(base_url):
        return False
    
    # 2. 检查向量数据库
    if not check_vector_db_status(base_url):
        print("⚠️  向量数据库可能未初始化")
        print("💡 运行初始化脚本: python init_vector_db.py")
    
    # 3. 显示使用说明
    show_usage_instructions()
    
    # 4. 打开前端页面
    print("\n🌐 正在打开浏览器...")
    open_frontend_pages(base_url)
    
    print("\n🎉 前端演示已启动！")
    print("📱 请在浏览器中体验前端功能")
    
    return True


def main():
    """主函数"""
    print("🎯 Wren AI Flask Service - 前端功能演示")
    print("="*50)
    
    # 检查参数
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"
    
    print(f"🌐 服务地址: {base_url}")
    
    # 运行演示
    success = run_demo_workflow(base_url)
    
    if success:
        print("\n💡 提示:")
        print("  • 如果页面未自动打开，请手动访问:")
        print(f"    - 演示页面: {base_url}/demo")
        print(f"    - 主页面: {base_url}")
        print("  • 尝试输入问题并观察处理过程")
        print("  • 记得提供反馈帮助系统学习")
        
        # 保持脚本运行，方便用户查看说明
        try:
            input("\n按 Enter 键退出...")
        except KeyboardInterrupt:
            print("\n👋 演示结束")
    else:
        print("\n💥 演示启动失败")
        print("🔧 请检查:")
        print("  1. 服务是否已启动 (python start.py)")
        print("  2. 端口是否被占用")
        print("  3. 网络连接是否正常")
        sys.exit(1)


if __name__ == "__main__":
    main()
