/* Wren AI Flask Service - 前端样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 30px;
    text-align: center;
}

.header h1 {
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 300;
}

.header p {
    font-size: 1.1em;
    opacity: 0.9;
}

.main-content {
    padding: 30px;
}

.section {
    margin-bottom: 40px;
    padding: 25px;
    border-radius: 10px;
    background: #f8f9fa;
    border-left: 4px solid #4facfe;
}

.section h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.5em;
    font-weight: 500;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control:focus {
    outline: none;
    border-color: #4facfe;
    box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

.btn {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
}

.btn-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.result-container {
    margin-top: 20px;
    padding: 20px;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    background: white;
}

.result-container.success {
    border-color: #28a745;
    background: #f8fff9;
}

.result-container.error {
    border-color: #dc3545;
    background: #fff8f8;
}

.result-container.info {
    border-color: #17a2b8;
    background: #f8fcff;
}

.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.status-understanding { background: #fff3cd; color: #856404; }
.status-searching { background: #d4edda; color: #155724; }
.status-reasoning { background: #cce7ff; color: #004085; }
.status-generating { background: #f8d7da; color: #721c24; }
.status-validating { background: #e2e3e5; color: #383d41; }
.status-finished { background: #d1ecf1; color: #0c5460; }
.status-failed { background: #f5c6cb; color: #721c24; }

.sql-code {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    white-space: pre-wrap;
    overflow-x: auto;
    margin: 10px 0;
}

.feedback-buttons {
    margin-top: 15px;
    text-align: center;
}

.feedback-buttons .btn {
    margin: 0 10px;
    min-width: 100px;
}

.loading {
    display: none;
    text-align: center;
    padding: 20px;
}

.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4facfe;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    animation: spin 1s linear infinite;
    margin: 0 auto 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid #e1e5e9;
}

.stat-value {
    font-size: 2em;
    font-weight: bold;
    color: #4facfe;
    margin-bottom: 5px;
}

.stat-label {
    color: #666;
    font-size: 0.9em;
}

.history-item {
    background: white;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.history-question {
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.history-sql {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    font-family: monospace;
    font-size: 12px;
    margin-bottom: 10px;
}

.history-meta {
    font-size: 12px;
    color: #666;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.similarity-score {
    background: #4facfe;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 11px;
}

.alert {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid transparent;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.tabs {
    display: flex;
    border-bottom: 2px solid #e1e5e9;
    margin-bottom: 20px;
}

.tab {
    padding: 12px 20px;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab.active {
    border-bottom-color: #4facfe;
    color: #4facfe;
    font-weight: 500;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 10px;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .section {
        padding: 20px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .tabs {
        flex-wrap: wrap;
    }
    
    .tab {
        flex: 1;
        text-align: center;
        min-width: 120px;
    }
}
