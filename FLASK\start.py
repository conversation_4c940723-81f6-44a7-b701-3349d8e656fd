#!/usr/bin/env python3
"""
Wren AI Flask Service 启动脚本
"""

import os
import sys
import logging
from pathlib import Path

# 添加当前目录到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from config import Config


def check_environment():
    """检查环境配置"""
    print("🔍 Checking environment configuration...")

    # 检查必需的环境变量
    required_vars = [
        'DEEPSEEK_API_KEY',
    ]

    missing_vars = []
    for var in required_vars:
        if not getattr(Config, var, None):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file or environment variables.")
        return False

    print("✅ Environment configuration OK")
    return True


def check_dependencies():
    """检查依赖包"""
    print("📦 Checking dependencies...")

    required_packages = [
        'flask',
        'pydantic_ai',
        'openai',
        'pydantic',
        'python-dotenv',
        'aiohttp',
        'sqlglot',
        'cachetools'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"❌ Missing required packages: {', '.join(missing_packages)}")
        print("Please run: pip install -r requirements.txt")
        return False

    print("✅ Dependencies OK")
    return True


def setup_logging():
    """设置日志"""
    log_level = logging.DEBUG if Config.DEBUG else logging.INFO

    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('wren-ai-flask.log')
        ]
    )

    # 设置第三方库的日志级别
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)


def print_startup_info():
    """打印启动信息"""
    print("\n" + "="*60)
    print("🚀 Wren AI Flask Service")
    print("="*60)
    print(f"📍 Service URL: http://localhost:5000")
    print(f"🌐 Frontend UI: http://localhost:5000")
    print(f"🎯 Demo Page: http://localhost:5000/demo")
    print(f"🤖 LLM Model: {Config.DEEPSEEK_MODEL}")
    print(f"🔧 Debug Mode: {Config.DEBUG}")
    print(f"📊 Vector DB: {Config.CHROMA_COLLECTION_NAME}")
    print(f"⚡ Engine Endpoint: {Config.WREN_ENGINE_ENDPOINT}")
    print(f"🔄 Max Retries: {Config.MAX_SQL_CORRECTION_RETRIES}")
    print(f"⏱️  Engine Timeout: {Config.ENGINE_TIMEOUT}s")
    print("="*60)
    print("\n🌐 Frontend Pages:")
    print("  GET    /                          - Main UI")
    print("  GET    /demo                      - Demo page")
    print("\n📚 API Endpoints:")
    print("  POST   /v1/asks                    - Submit question")
    print("  GET    /v1/asks/{id}/result       - Get result")
    print("  GET    /v1/asks/{id}/status       - Get status")
    print("  PATCH  /v1/asks/{id}              - Stop processing")
    print("  POST   /v1/asks/{id}/feedback     - Submit feedback")
    print("  POST   /v1/vector-db/store        - Store Q&A pair")
    print("  GET    /v1/vector-db/stats        - Vector DB stats")
    print("  GET    /health                    - Health check")
    if Config.DEBUG:
        print("  GET    /v1/debug/workflow/{id}    - Debug info")
    print("\n🧪 Test the service:")
    print("  python test_api.py              - API tests")
    print("  python test_vector_db.py        - Vector DB tests")
    print("  python test_frontend.py         - Frontend tests")
    print("  python example_usage.py         - Usage examples")
    print("\n💡 Quick Start:")
    print("  1. Open http://localhost:5000 in your browser")
    print("  2. Try the demo at http://localhost:5000/demo")
    print("  3. Ask questions like 'How many customers do we have?'")
    print("  4. Provide feedback to improve the system")
    print("\n" + "="*60)


def main():
    """主函数"""
    print("🔧 Starting Wren AI Flask Service...")

    # 检查环境
    if not check_environment():
        sys.exit(1)

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    # 设置日志
    setup_logging()

    # 打印启动信息
    print_startup_info()

    # 启动应用
    try:
        from app import app
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=Config.DEBUG,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 Service stopped by user")
    except Exception as e:
        print(f"\n💥 Service failed to start: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
