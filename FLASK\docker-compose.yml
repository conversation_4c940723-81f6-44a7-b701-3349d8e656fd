version: '3.8'

services:
  wren-ai-flask:
    build: .
    ports:
      - "5000:5000"
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEEPSEEK_BASE_URL=${DEEPSEEK_BASE_URL:-https://api.deepseek.com/v1}
      - DEEPSEEK_MODEL=${DEEPSEEK_MODEL:-deepseek-chat}
      - DEBUG=${DEBUG:-False}
      - WREN_ENGINE_ENDPOINT=${WREN_ENGINE_ENDPOINT:-http://localhost:8080}
      - WREN_IBIS_ENDPOINT=${WREN_IBIS_ENDPOINT:-http://localhost:8000}
      - MAX_SQL_CORRECTION_RETRIES=${MAX_SQL_CORRECTION_RETRIES:-3}
      - ENGINE_TIMEOUT=${ENGINE_TIMEOUT:-30.0}
      - CACHE_TTL=${CACHE_TTL:-120}
      - CACHE_MAXSIZE=${CACHE_MAXSIZE:-1000000}
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  logs:
