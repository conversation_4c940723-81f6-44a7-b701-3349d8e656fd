# Wren AI Flask Service - 功能式样书

## 文档信息

- **项目名称**: Wren AI Flask Service
- **版本**: 1.0.0
- **创建日期**: 2024年
- **文档类型**: 功能式样书 (Functional Specification)
- **技术栈**: PydanticAI + DeepSeek + Chroma + Flask

## 1. 系统概述

### 1.1 系统简介

Wren AI Flask Service 是一个基于 PydanticAI Agent Workflow 架构的智能SQL生成系统。系统通过自然语言处理技术，将用户的自然语言问题转换为相应的SQL查询语句，并提供完整的向量数据库支持以实现历史问题检索和持续学习优化。

### 1.2 核心特性

- **智能意图识别**: 自动分类用户问题类型
- **历史问题检索**: 基于Chroma向量数据库的语义检索
- **多Agent协作**: 采用PydanticAI框架的Agent工作流
- **错误处理与重试**: 完整的错误反馈和自动重试机制
- **持续学习**: 基于用户反馈的系统优化
- **前端界面**: jQuery + HTML的现代化Web界面

### 1.3 技术架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐                  │
│  │   Main UI       │  │   Demo Page     │                  │
│  │  (index.html)   │  │  (demo.html)    │                  │
│  └─────────────────┘  └─────────────────┘                  │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                     API Layer                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Flask Application                        │ │
│  │  /v1/asks, /v1/feedback, /v1/vector-db/*              │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Service Layer                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              WorkflowService                            │ │
│  │         (Agent协调和状态管理)                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Agent Layer                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │HistoricalQ  │ │ IntentAgent │ │ SchemaAgent │           │
│  │   Agent     │ │             │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │SqlReasoning │ │SqlGeneration│ │SqlValidation│           │
│  │   Agent     │ │   Agent     │ │   Agent     │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                External Services                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  DeepSeek   │ │   Chroma    │ │ Wren Engine │           │
│  │    LLM      │ │ Vector DB   │ │             │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 1.4 工作流程概述

```mermaid
graph TD
    A[用户提交问题] --> B[历史问题检索]
    B --> C{找到相似问题?}
    C -->|是| D[直接返回历史SQL]
    C -->|否| E[并行检索资源]
    E --> F[意图分类]
    F --> G{意图类型}
    G -->|SQL_QUERY| H[表结构检索]
    G -->|GENERAL| I[通用回答]
    G -->|MISLEADING| J[错误提示]
    H --> K[SQL推理]
    K --> L[SQL生成]
    L --> M[SQL验证]
    M --> N{验证通过?}
    N -->|是| O[返回结果]
    N -->|否| P{重试次数<最大值?}
    P -->|是| Q[错误反馈]
    Q --> K
    P -->|否| R[返回失败]
    O --> S[自动存储到向量DB]
    D --> T[用户反馈]
    O --> T
    T --> U[更新向量DB权重]
```

## 2. 系统配置

### 2.1 环境变量配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| DEEPSEEK_API_KEY | string | - | DeepSeek API密钥 (必需) |
| DEEPSEEK_BASE_URL | string | https://api.deepseek.com/v1 | DeepSeek API基础URL |
| DEEPSEEK_MODEL | string | deepseek-chat | 使用的DeepSeek模型 |
| DEBUG | boolean | False | Flask调试模式 |
| SECRET_KEY | string | - | Flask密钥 |
| CHROMA_PERSIST_DIRECTORY | string | ./chroma_db | Chroma数据库存储目录 |
| CHROMA_COLLECTION_NAME | string | historical_questions | Chroma集合名称 |
| EMBEDDING_MODEL | string | all-MiniLM-L6-v2 | 文本嵌入模型 |
| SIMILARITY_THRESHOLD | float | 0.7 | 相似度阈值 |
| MAX_HISTORICAL_RESULTS | int | 3 | 最大历史结果数量 |
| MAX_SQL_CORRECTION_RETRIES | int | 3 | 最大SQL重试次数 |
| ENGINE_TIMEOUT | float | 30.0 | 引擎超时时间(秒) |
| CACHE_TTL | int | 120 | 缓存生存时间(秒) |
| CACHE_MAXSIZE | int | 1000000 | 缓存最大大小 |

### 2.2 系统依赖

```python
# 核心依赖
flask==3.0.0                 # Web框架
pydantic-ai==0.0.14         # Agent框架
openai==1.54.3              # LLM客户端
pydantic==2.9.2             # 数据验证
python-dotenv==1.0.0        # 环境变量管理

# 向量数据库
chromadb==0.4.22            # 向量数据库
sentence-transformers==2.2.2 # 文本嵌入

# 工具库
sqlglot==25.27.0            # SQL解析和验证
aiohttp==3.9.1              # 异步HTTP客户端
cachetools==5.5.0           # 缓存工具
requests==2.31.0            # HTTP客户端
orjson==3.10.7              # JSON处理
numpy==1.24.3               # 数值计算
```

## 3. 核心组件架构

### 3.1 Agent基类设计

所有Agent都继承自`BaseAgent`类，提供统一的LLM调用接口和日志记录功能。

```python
class BaseAgent:
    """所有 Agent 的基类"""

    def __init__(self, model_name: str = None):
        self.client = AsyncOpenAI(
            api_key=Config.DEEPSEEK_API_KEY,
            base_url=Config.DEEPSEEK_BASE_URL
        )
        self.model_name = model_name or Config.DEEPSEEK_MODEL

    def create_agent(self, system_prompt: str, result_type: Any = str) -> Agent:
        """创建 PydanticAI Agent"""

    async def call_llm(self, prompt: str, system_prompt: str = "", **kwargs) -> str:
        """直接调用 LLM"""

    def log_agent_action(self, action: str, details: Dict[str, Any]):
        """记录 Agent 行为"""
```

### 3.2 工作流服务设计

`WorkflowService`是系统的核心协调器，负责管理Agent的执行顺序和状态转换。

```python
class WorkflowService:
    """工作流服务 - 协调各个 Agent 的执行"""

    def __init__(self):
        # 初始化所有 Agent
        self.intent_agent = IntentAgent()
        self.schema_agent = SchemaRetrievalAgent()
        self.reasoning_agent = SqlReasoningAgent()
        self.generation_agent = SqlGenerationAgent()
        self.validation_agent = SqlValidationAgent()
        self.historical_question_agent = HistoricalQuestionAgent()
        self.sql_pairs_agent = SqlPairsRetrievalAgent()
        self.instructions_agent = InstructionsRetrievalAgent()
        self.sql_functions_agent = SqlFunctionsAgent()

    async def process_ask_request(self, ...) -> str:
        """处理用户问题请求"""

    async def _execute_workflow(self, query_id: str):
        """执行完整的工作流"""

    async def handle_feedback(self, query_id: str, is_helpful: bool) -> bool:
        """处理用户反馈"""
```

### 3.3 向量数据库服务

`VectorDBService`基于Chroma实现，提供语义检索和反馈学习功能。

```python
class VectorDBService:
    """向量数据库服务 - 基于 Chroma"""

    def __init__(self):
        self.client = chromadb.PersistentClient(...)
        self.embedding_model = SentenceTransformer(...)
        self.collection = self._get_or_create_collection()

    async def store_question_sql_pair(self, ...) -> str:
        """存储问题-SQL对"""

    async def search_similar_questions(self, ...) -> List[Dict[str, Any]]:
        """搜索相似问题"""

    async def update_feedback(self, ...) -> bool:
        """更新反馈"""

## 4. Agent详细规格

### 4.1 HistoricalQuestionAgent (历史问题检索Agent)

**功能描述**: 基于Chroma向量数据库检索历史相似问题，实现快速响应和持续学习。

**类定义**:
```python
class HistoricalQuestionAgent(BaseAgent):
    """历史问题检索 Agent - 基于向量数据库的语义检索"""
```

**核心方法**:

#### 4.1.1 search_similar_questions
```python
async def search_similar_questions(
    self,
    question: str,
    project_id: Optional[str] = None,
    similarity_threshold: float = 0.7,
    max_results: int = 3
) -> List[Dict[str, Any]]
```

**参数说明**:
- `question`: 用户输入的问题
- `project_id`: 项目ID，用于数据隔离
- `similarity_threshold`: 相似度阈值 (0.0-1.0)
- `max_results`: 最大返回结果数量

**返回值**:
```python
[
    {
        "question": "How many customers do we have?",
        "sql": "SELECT COUNT(*) FROM customers",
        "similarity": 0.95,
        "metadata": {...},
        "feedback_count": 5,
        "helpful_count": 4
    }
]
```

#### 4.1.2 store_question_sql_pair
```python
async def store_question_sql_pair(
    self,
    question: str,
    sql: str,
    project_id: Optional[str] = None,
    view_id: Optional[str] = None,
    metadata: Optional[dict] = None
) -> str
```

**功能**: 存储成功的问题-SQL对到向量数据库
**返回值**: 文档ID (string)

#### 4.1.3 update_feedback
```python
async def update_feedback(
    self,
    question: str,
    is_helpful: bool,
    project_id: Optional[str] = None
) -> bool
```

**功能**: 更新用户反馈，影响后续检索权重
**返回值**: 操作成功状态 (boolean)

### 4.2 IntentAgent (意图分类Agent)

**功能描述**: 分析用户问题意图，分类为SQL查询、通用问题或误导性问题。

**类定义**:
```python
class IntentAgent(BaseAgent):
    """意图分类 Agent"""
```

**核心方法**:

#### 4.2.1 classify_intent
```python
async def classify_intent(
    self,
    query: str,
    histories: List[AskHistory] = None,
    sql_samples: List[dict] = None,
    instructions: List[dict] = None,
    project_id: Optional[str] = None,
    configuration: Configuration = None
) -> IntentResult
```

**参数说明**:
- `query`: 用户问题
- `histories`: 历史对话记录
- `sql_samples`: SQL样例数据
- `instructions`: 指令数据
- `project_id`: 项目ID
- `configuration`: 配置信息

**返回值结构**:
```python
class IntentResult(BaseModel):
    intent: Literal["SQL_QUERY", "GENERAL", "MISLEADING_QUERY"]
    reasoning: str
    rephrased_question: Optional[str] = None
    confidence: float  # 0.0-1.0
```

**意图分类规则**:
- **SQL_QUERY**: 用户想要查询数据库中的具体数据
- **GENERAL**: 用户询问数据结构、定义或需要帮助理解数据
- **MISLEADING_QUERY**: 问题模糊、包含不明确引用或询问不存在的数据

**系统提示词**:
```
### TASK ###
You are a helpful data analyst who is great at understanding user's question and classifying the intent.

### CLASSIFICATION RULES ###
- If the user is asking for specific data, metrics, or wants to analyze data, classify as SQL_QUERY
- If the user is asking general questions about the data structure, definitions, or needs help understanding the data, classify as GENERAL
- If the question is too vague, contains unclear references, or asks for data that doesn't exist, classify as MISLEADING_QUERY
```

### 4.3 SchemaRetrievalAgent (表结构检索Agent)

**功能描述**: 根据用户问题检索相关的数据库表结构和字段信息。

**类定义**:
```python
class SchemaRetrievalAgent(BaseAgent):
    """表结构检索 Agent"""
```

**核心方法**:

#### 4.3.1 retrieve_schema
```python
async def retrieve_schema(
    self,
    query: str,
    project_id: Optional[str] = None,
    table_names: List[str] = None,
    configuration: Optional[Configuration] = None,
    enable_column_pruning: bool = False
) -> SchemaRetrievalResult
```

**参数说明**:
- `query`: 用户问题
- `project_id`: 项目ID
- `table_names`: 指定的表名列表
- `configuration`: 配置信息
- `enable_column_pruning`: 是否启用列裁剪

**返回值结构**:
```python
class SchemaRetrievalResult(BaseModel):
    table_ddls: List[str]  # DDL语句列表
    table_names: List[str]  # 表名列表
    has_calculated_field: bool  # 是否包含计算字段
    has_metric: bool  # 是否包含指标
```

**智能匹配逻辑**:
- 根据问题中的关键词匹配相关表
- 支持客户、订单、产品等业务实体识别
- 自动检测计算字段和指标需求

### 4.4 SqlReasoningAgent (SQL推理Agent)

**功能描述**: 基于问题和表结构进行逻辑推理，生成解决方案的步骤计划。

**类定义**:
```python
class SqlReasoningAgent(BaseAgent):
    """SQL 推理 Agent"""
```

**核心方法**:

#### 4.4.1 generate_reasoning
```python
async def generate_reasoning(
    self,
    query: str,
    table_ddls: List[str],
    histories: List[AskHistory] = None,
    sql_samples: List[dict] = None,
    instructions: List[dict] = None,
    configuration: Configuration = None,
    error_feedback: Optional[str] = None
) -> SqlReasoningResult
```

**参数说明**:
- `query`: 用户问题
- `table_ddls`: 数据库表结构DDL
- `histories`: 历史对话
- `sql_samples`: SQL样例
- `instructions`: 指令信息
- `configuration`: 配置
- `error_feedback`: 错误反馈信息

**返回值结构**:
```python
class SqlReasoningResult(BaseModel):
    reasoning: str  # 详细推理过程
    step_by_step_plan: List[str]  # 分步解决方案
```

**推理示例**:
```json
{
    "reasoning": "用户想要找到每个客户的总销售额。我需要连接customers表和orders表，并对total_amount进行求和。",
    "step_by_step_plan": [
        "连接customers表和orders表，使用customer_id",
        "按客户信息分组",
        "对每个客户的total_amount求和",
        "按总销售额降序排列"
    ]
}
```

### 4.5 SqlGenerationAgent (SQL生成Agent)

**功能描述**: 根据推理结果生成具体的SQL查询语句。

**类定义**:
```python
class SqlGenerationAgent(BaseAgent):
    """SQL 生成 Agent"""
```

**核心方法**:

#### 4.5.1 generate_sql
```python
async def generate_sql(
    self,
    query: str,
    table_ddls: List[str],
    reasoning: str,
    histories: List[AskHistory] = None,
    sql_samples: List[dict] = None,
    instructions: List[dict] = None,
    configuration: Configuration = None,
    has_calculated_field: bool = False,
    has_metric: bool = False,
    sql_functions: List[SqlFunction] = None,
    error_feedback: Optional[str] = None
) -> SqlGenerationResult
```

**参数说明**:
- `query`: 用户问题
- `table_ddls`: 表结构DDL
- `reasoning`: 推理结果
- `histories`: 历史对话
- `sql_samples`: SQL样例
- `instructions`: 指令
- `configuration`: 配置
- `has_calculated_field`: 是否有计算字段
- `has_metric`: 是否有指标
- `sql_functions`: 可用SQL函数
- `error_feedback`: 错误反馈

**返回值结构**:
```python
class SqlGenerationResult(BaseModel):
    sql: str  # 生成的SQL语句
    correlation_id: Optional[str] = None  # 关联ID
```

**生成规则**:
- 生成符合ANSI SQL标准的查询
- 考虑性能优化和最佳实践
- 支持错误反馈的迭代改进
- 集成可用的SQL函数

### 4.6 SqlValidationAgent (SQL验证Agent)

**功能描述**: 验证生成的SQL语法正确性和执行可行性。

**类定义**:
```python
class SqlValidationAgent(BaseAgent):
    """SQL 验证 Agent"""
```

**核心方法**:

#### 4.6.1 validate_sql
```python
async def validate_sql(
    self,
    sql: str,
    project_id: Optional[str] = None,
    timeout: Optional[float] = None
) -> SqlValidationResult
```

**参数说明**:
- `sql`: 待验证的SQL语句
- `project_id`: 项目ID
- `timeout`: 超时时间(秒)

**返回值结构**:
```python
class SqlValidationResult(BaseModel):
    is_valid: bool  # 是否有效
    error_message: Optional[str] = None  # 错误信息
    error_type: Optional[str] = None  # 错误类型
```

**验证流程**:
1. **语法验证**: 使用sqlglot进行SQL语法检查
2. **执行验证**: 通过Wren Engine进行dry run测试
3. **错误分类**: 区分语法错误和执行错误

**错误类型**:
- `SYNTAX`: 语法错误
- `EXECUTION`: 执行错误
- `TIMEOUT`: 超时错误

### 4.7 SqlPairsRetrievalAgent (SQL样例检索Agent)

**功能描述**: 检索相关的SQL查询样例，为生成提供参考。

**核心方法**:
```python
async def retrieve_sql_pairs(
    self,
    query: str,
    project_id: Optional[str] = None
) -> List[dict]
```

### 4.8 InstructionsRetrievalAgent (指令检索Agent)

**功能描述**: 检索相关的查询指令和规则。

**核心方法**:
```python
async def retrieve_instructions(
    self,
    query: str,
    project_id: Optional[str] = None
) -> List[dict]
```

### 4.9 SqlFunctionsAgent (SQL函数检索Agent)

**功能描述**: 检索可用的SQL函数和其使用说明。

**核心方法**:
```python
async def retrieve_sql_functions(
    self,
    query: str,
    project_id: Optional[str] = None
) -> List[SqlFunction]
```

**返回值结构**:
```python
class SqlFunction(BaseModel):
    name: str  # 函数名
    description: str  # 函数描述
    syntax: str  # 语法格式
    example: Optional[str] = None  # 使用示例
```

## 5. 工作流详细规格

### 5.1 WorkflowService (工作流服务)

**功能描述**: 系统的核心协调器，负责管理Agent的执行顺序、状态转换和错误处理。

**类定义**:
```python
class WorkflowService:
    """工作流服务 - 协调各个 Agent 的执行"""

    def __init__(self):
        # 初始化所有 Agent
        self.intent_agent = IntentAgent()
        self.schema_agent = SchemaRetrievalAgent()
        self.reasoning_agent = SqlReasoningAgent()
        self.generation_agent = SqlGenerationAgent()
        self.validation_agent = SqlValidationAgent()
        self.historical_question_agent = HistoricalQuestionAgent()
        self.sql_pairs_agent = SqlPairsRetrievalAgent()
        self.instructions_agent = InstructionsRetrievalAgent()
        self.sql_functions_agent = SqlFunctionsAgent()
```

### 5.2 核心工作流方法

#### 5.2.1 process_ask_request
```python
async def process_ask_request(
    self,
    query: str,
    project_id: Optional[str] = None,
    mdl_hash: Optional[str] = None,
    thread_id: Optional[str] = None,
    histories: List[AskHistory] = None,
    configurations: Configuration = None,
    ignore_sql_generation_reasoning: bool = False,
    enable_column_pruning: bool = False
) -> str
```

**功能**: 处理用户问题请求的入口方法
**返回值**: 查询ID (string)

**处理流程**:
1. 生成唯一的查询ID
2. 初始化工作流状态
3. 异步启动工作流执行
4. 立即返回查询ID

#### 5.2.2 _execute_workflow
```python
async def _execute_workflow(self, query_id: str)
```

**功能**: 执行完整的工作流程

**执行步骤**:
1. **历史问题检索** - 检查是否有相似的历史问题
2. **并行资源检索** - 同时检索SQL样例、指令和函数
3. **意图分类** - 分析用户问题意图
4. **分支执行** - 根据意图执行不同的工作流

### 5.3 工作流状态管理

#### 5.3.1 WorkflowState数据结构
```python
class WorkflowState(BaseModel):
    # 基本信息
    query_id: str
    user_query: str
    project_id: Optional[str] = None
    histories: List[AskHistory] = Field(default_factory=list)
    configuration: Configuration = Field(default_factory=Configuration)

    # 额外配置
    mdl_hash: Optional[str] = None
    thread_id: Optional[str] = None
    ignore_sql_generation_reasoning: bool = False
    enable_column_pruning: bool = False

    # 执行状态
    status: str = "understanding"  # understanding, searching, reasoning, generating, validating, finished, failed
    retry_count: int = 0
    error_messages: List[str] = Field(default_factory=list)

    # Agent执行结果
    historical_question_result: Optional[List[dict]] = None
    intent_result: Optional[IntentResult] = None
    schema_result: Optional[SchemaRetrievalResult] = None
    reasoning_result: Optional[SqlReasoningResult] = None
    generation_result: Optional[SqlGenerationResult] = None
    validation_result: Optional[SqlValidationResult] = None

    # 检索资源
    sql_samples: List[dict] = Field(default_factory=list)
    instructions: List[dict] = Field(default_factory=list)
    sql_functions: List[SqlFunction] = Field(default_factory=list)
```

#### 5.3.2 状态转换图
```
understanding → searching → reasoning → generating → validating → finished
     ↓              ↓           ↓           ↓           ↓
   failed        failed      failed      failed      failed
                                          ↓ (重试)
                                      reasoning
```

### 5.4 详细工作流步骤

#### 5.4.1 步骤1: 历史问题检索
```python
async def _step_historical_question_retrieval(self, state: WorkflowState)
```

**状态**: `understanding`
**功能**: 在向量数据库中检索相似的历史问题
**决策**: 如果找到相似度高的历史问题，直接返回结果，跳过后续步骤

**检索逻辑**:
- 使用语义相似度搜索
- 相似度阈值: 0.7 (可配置)
- 最大结果数: 3 (可配置)
- 考虑用户反馈权重

#### 5.4.2 步骤2: 并行资源检索
```python
async def _step_parallel_retrieval(self, state: WorkflowState)
```

**功能**: 并行执行三个检索任务
- **SQL样例检索**: 获取相关的SQL查询示例
- **指令检索**: 获取查询指令和规则
- **SQL函数检索**: 获取可用的SQL函数

**并行执行**:
```python
sql_samples, instructions, sql_functions = await asyncio.gather(
    self.sql_pairs_agent.retrieve_sql_pairs(...),
    self.instructions_agent.retrieve_instructions(...),
    self.sql_functions_agent.retrieve_sql_functions(...)
)
```

#### 5.4.3 步骤3: 意图分类
```python
async def _step_intent_classification(self, state: WorkflowState)
```

**状态**: `understanding`
**功能**: 分析用户问题意图
**输入**: 用户问题 + 历史记录 + 检索资源
**输出**: 意图分类结果

**分支决策**:
- `SQL_QUERY` → 执行SQL生成工作流
- `GENERAL` → 执行通用问题工作流
- `MISLEADING_QUERY` → 执行误导性问题工作流

#### 5.4.4 步骤4: 表结构检索 (SQL工作流)
```python
async def _step_schema_retrieval(self, state: WorkflowState)
```

**状态**: `searching`
**功能**: 检索相关的数据库表结构
**智能匹配**: 根据问题关键词匹配相关表
**输出**: DDL语句、表名列表、计算字段标识、指标标识

#### 5.4.5 步骤5: SQL推理 (可选)
```python
async def _step_sql_reasoning(self, state: WorkflowState)
```

**状态**: `reasoning`
**功能**: 生成解决问题的逻辑推理
**条件**: 当 `ignore_sql_generation_reasoning = False` 时执行
**输出**: 详细推理过程和分步计划

#### 5.4.6 步骤6: SQL生成与验证 (带重试)
```python
async def _step_sql_generation_with_retry(self, state: WorkflowState)
```

**状态**: `generating` → `validating`
**功能**: 生成SQL并验证，支持错误反馈重试

**重试机制**:
- 最大重试次数: 3 (可配置)
- 错误反馈: 将验证错误信息传递给下次生成
- 推理更新: 验证失败时重新执行推理步骤

**验证流程**:
1. **语法验证**: 使用sqlglot检查SQL语法
2. **执行验证**: 通过Wren Engine进行dry run
3. **错误分类**: 区分语法错误和执行错误

### 5.5 错误处理机制

#### 5.5.1 重试策略
```python
# SQL生成重试逻辑
for attempt in range(max_retries + 1):
    # 生成SQL
    generation_result = await self.generation_agent.generate_sql(...)

    # 验证SQL
    validation_result = await self.validation_agent.validate_sql(...)

    if validation_result.is_valid:
        # 成功，完成工作流
        await self._complete_sql_workflow(state)
        return
    else:
        # 失败，记录错误并准备重试
        state.error_messages.append(validation_result.error_message)
        state.retry_count = attempt + 1

        if attempt < max_retries:
            # 重新推理
            await self._step_sql_reasoning_with_feedback(state)
        else:
            # 达到最大重试次数
            await self._handle_sql_generation_failure(state)
```

#### 5.5.2 错误类型处理
- **语法错误**: 重新推理和生成
- **执行错误**: 重新推理和生成
- **超时错误**: 直接失败
- **系统错误**: 记录日志并返回通用错误

### 5.6 工作流分支

#### 5.6.1 SQL查询工作流
```
意图分类(SQL_QUERY) → 表结构检索 → SQL推理 → SQL生成 → SQL验证 → 完成
                                    ↑                    ↓
                                    └── 错误反馈重试 ←────┘
```

#### 5.6.2 通用问题工作流
```
意图分类(GENERAL) → 确定通用类型 → 构建响应 → 完成
```

**通用类型**:
- `DATA_ASSISTANCE`: 数据相关帮助
- `USER_GUIDE`: 用户指南

#### 5.6.3 误导性问题工作流
```
意图分类(MISLEADING_QUERY) → 构建错误响应 → 完成
```

### 5.7 缓存和性能优化

#### 5.7.1 状态缓存
```python
self.workflow_states: Dict[str, WorkflowState] = TTLCache(
    maxsize=Config.CACHE_MAXSIZE,  # 1000000
    ttl=Config.CACHE_TTL           # 120秒
)
```

#### 5.7.2 结果缓存
```python
self.ask_results: Dict[str, AskResultResponse] = TTLCache(
    maxsize=Config.CACHE_MAXSIZE,
    ttl=Config.CACHE_TTL
)
```

#### 5.7.3 并行执行优化
- 资源检索并行化
- 异步任务执行
- 非阻塞状态更新

### 5.8 监控和日志

#### 5.8.1 状态跟踪
每个工作流步骤都会更新状态，便于前端实时显示进度：
- `understanding`: 理解问题/意图分类
- `searching`: 检索数据/表结构
- `reasoning`: SQL推理
- `generating`: SQL生成
- `validating`: SQL验证
- `finished`: 完成
- `failed`: 失败

#### 5.8.2 性能指标
- 工作流执行时间
- Agent调用次数
- 重试次数统计
- 成功率统计

## 6. 向量数据库详细规格

### 6.1 VectorDBService (向量数据库服务)

**功能描述**: 基于Chroma实现的向量数据库服务，提供语义检索和反馈学习功能。

**类定义**:
```python
class VectorDBService:
    """向量数据库服务 - 基于 Chroma"""

    def __init__(self):
        self.client = chromadb.PersistentClient(
            path=Config.CHROMA_PERSIST_DIRECTORY
        )
        self.embedding_model = SentenceTransformer(Config.EMBEDDING_MODEL)
        self.collection = self._get_or_create_collection()
```

### 6.2 核心功能方法

#### 6.2.1 存储问题-SQL对
```python
async def store_question_sql_pair(
    self,
    question: str,
    sql: str,
    project_id: Optional[str] = None,
    view_id: Optional[str] = None,
    metadata: Optional[dict] = None
) -> str
```

**功能**: 将问题-SQL对存储到向量数据库
**处理流程**:
1. 生成文本嵌入向量
2. 构建元数据
3. 存储到Chroma集合
4. 返回文档ID

#### 6.2.2 搜索相似问题
```python
async def search_similar_questions(
    self,
    question: str,
    project_id: Optional[str] = None,
    similarity_threshold: float = 0.7,
    max_results: int = 3
) -> List[Dict[str, Any]]
```

**功能**: 基于语义相似度搜索历史问题
**返回格式**:
```python
[
    {
        "question": "How many customers do we have?",
        "sql": "SELECT COUNT(*) FROM customers",
        "similarity": 0.95,
        "metadata": {...},
        "feedback_count": 5,
        "helpful_count": 4
    }
]
```

#### 6.2.3 更新反馈
```python
async def update_feedback(
    self,
    question: str,
    is_helpful: bool,
    project_id: Optional[str] = None
) -> bool
```

**功能**: 更新用户反馈，影响后续检索权重

### 6.3 数据存储结构

#### 6.3.1 Chroma集合配置
```python
collection_metadata = {
    "hnsw:space": "cosine",  # 余弦相似度
    "hnsw:construction_ef": 200,
    "hnsw:M": 16
}
```

#### 6.3.2 文档元数据结构
```python
{
    "project_id": "demo_project",
    "view_id": "customer_view",
    "created_at": "2024-01-01T00:00:00Z",
    "feedback_count": 5,
    "helpful_count": 4,
    "last_used": "2024-01-01T00:00:00Z",
    "usage_count": 10,
    "category": "customer_analysis",
    "difficulty": "easy",
    "tables": ["customers", "orders"]
}
```

## 7. 数据模型详细规格

### 7.1 核心数据模型

#### 7.1.1 AskHistory (历史问答记录)
```python
class AskHistory(BaseModel):
    sql: str        # SQL查询语句
    question: str   # 用户问题
```

**用途**: 存储历史对话记录，用于上下文理解

#### 7.1.2 Configuration (配置信息)
```python
class Configuration(BaseModel):
    language: Optional[str] = "English"     # 语言设置
    fiscal_year: Optional[dict] = None      # 财年配置
```

**用途**: 系统配置和用户偏好设置

#### 7.1.3 SqlFunction (SQL函数)
```python
class SqlFunction(BaseModel):
    name: str                               # 函数名
    description: str                        # 函数描述
    parameters: List[dict] = Field(default_factory=list)  # 参数列表
```

**用途**: 描述可用的SQL函数和其参数

### 7.2 Agent结果模型

#### 7.2.1 IntentResult (意图分类结果)
```python
class IntentResult(BaseModel):
    intent: Literal["SQL_QUERY", "GENERAL", "MISLEADING_QUERY"]  # 意图类型
    reasoning: str                          # 分类推理过程
    rephrased_question: Optional[str] = None  # 重新表述的问题
    confidence: float = 0.0                 # 置信度 (0.0-1.0)
```

**意图类型说明**:
- `SQL_QUERY`: 需要生成SQL查询
- `GENERAL`: 通用问题，需要提供帮助信息
- `MISLEADING_QUERY`: 误导性或无法回答的问题

#### 7.2.2 SchemaRetrievalResult (表结构检索结果)
```python
class SchemaRetrievalResult(BaseModel):
    table_ddls: List[str]                   # DDL语句列表
    table_names: List[str]                  # 表名列表
    has_calculated_field: bool = False      # 是否包含计算字段
    has_metric: bool = False                # 是否包含指标
```

**用途**: 存储检索到的相关表结构信息

#### 7.2.3 SqlReasoningResult (SQL推理结果)
```python
class SqlReasoningResult(BaseModel):
    reasoning: str                          # 详细推理过程
    step_by_step_plan: List[str]           # 分步解决方案
```

**用途**: 存储SQL生成的逻辑推理过程

#### 7.2.4 SqlGenerationResult (SQL生成结果)
```python
class SqlGenerationResult(BaseModel):
    sql: str                                # 生成的SQL语句
    correlation_id: Optional[str] = None    # 关联ID
```

**用途**: 存储生成的SQL查询语句

#### 7.2.5 SqlValidationResult (SQL验证结果)
```python
class SqlValidationResult(BaseModel):
    is_valid: bool                          # 是否有效
    error_message: Optional[str] = None     # 错误信息
    error_type: Optional[Literal["SYNTAX", "EXECUTION", "TIMEOUT"]] = None  # 错误类型
```

**错误类型说明**:
- `SYNTAX`: SQL语法错误
- `EXECUTION`: SQL执行错误
- `TIMEOUT`: 验证超时

### 7.3 请求模型

#### 7.3.1 AskRequest (问答请求)
```python
class AskRequest(BaseModel):
    query: str                              # 用户问题 (必需)
    project_id: Optional[str] = None        # 项目ID
    mdl_hash: Optional[str] = None          # 模型哈希值
    thread_id: Optional[str] = None         # 线程ID
    histories: List[AskHistory] = Field(default_factory=list)  # 历史记录
    configurations: Configuration = Field(default_factory=Configuration)  # 配置
    ignore_sql_generation_reasoning: bool = False  # 是否跳过推理步骤
    enable_column_pruning: bool = False     # 是否启用列裁剪
```

**使用示例**:
```json
{
    "query": "How many customers do we have?",
    "project_id": "demo_project",
    "configurations": {
        "language": "English"
    }
}
```

#### 7.3.2 FeedbackRequest (反馈请求)
```python
class FeedbackRequest(BaseModel):
    query_id: str                           # 查询ID (必需)
    is_helpful: bool                        # 是否有帮助 (必需)
    comment: Optional[str] = None           # 评论内容
```

**使用示例**:
```json
{
    "query_id": "123e4567-e89b-12d3-a456-426614174000",
    "is_helpful": true,
    "comment": "This SQL is exactly what I needed!"
}
```

#### 7.3.3 StoreQuestionSqlRequest (存储请求)
```python
class StoreQuestionSqlRequest(BaseModel):
    question: str                           # 问题 (必需)
    sql: str                               # SQL语句 (必需)
    project_id: Optional[str] = None        # 项目ID
    view_id: Optional[str] = None          # 视图ID
    metadata: Optional[dict] = None         # 元数据
```

**使用示例**:
```json
{
    "question": "How many orders were placed today?",
    "sql": "SELECT COUNT(*) FROM orders WHERE DATE(order_date) = CURRENT_DATE",
    "project_id": "demo_project",
    "metadata": {
        "category": "daily_report",
        "difficulty": "easy"
    }
}
```

### 7.4 响应模型

#### 7.4.1 AskResponse (问答响应)
```python
class AskResponse(BaseModel):
    query_id: str                           # 查询ID
```

**用途**: 立即返回查询ID，用于后续状态查询

#### 7.4.2 AskResult (问答结果)
```python
class AskResult(BaseModel):
    sql: str                                # SQL查询语句
    type: Literal["llm", "view"] = "llm"   # 结果类型
    viewId: Optional[str] = None            # 视图ID
```

**结果类型说明**:
- `llm`: LLM生成的SQL
- `view`: 来自预定义视图的SQL

#### 7.4.3 AskError (错误信息)
```python
class AskError(BaseModel):
    code: Literal["NO_RELEVANT_DATA", "NO_RELEVANT_SQL", "OTHERS"]  # 错误代码
    message: str                            # 错误消息
```

**错误代码说明**:
- `NO_RELEVANT_DATA`: 没有相关数据
- `NO_RELEVANT_SQL`: 无法生成相关SQL
- `OTHERS`: 其他错误

#### 7.4.4 AskResultResponse (完整问答响应)
```python
class AskResultResponse(BaseModel):
    status: Literal["understanding", "searching", "reasoning", "generating", "validating", "finished", "failed", "stopped"]
    response: List[AskResult] = []          # 结果列表
    error: Optional[AskError] = None        # 错误信息
    trace_id: Optional[str] = None          # 追踪ID
    is_followup: bool = False               # 是否为后续问题
    rephrased_question: Optional[str] = None  # 重新表述的问题
    intent_reasoning: Optional[str] = None   # 意图推理
    sql_generation_reasoning: Optional[str] = None  # SQL生成推理
    type: Optional[Literal["TEXT_TO_SQL", "GENERAL"]] = None  # 响应类型
    retrieved_tables: Optional[List[str]] = None  # 检索到的表
    invalid_sql: Optional[str] = None       # 无效的SQL
    general_type: Optional[Literal["MISLEADING_QUERY", "DATA_ASSISTANCE", "USER_GUIDE"]] = None  # 通用类型
```

**状态说明**:
- `understanding`: 理解问题中
- `searching`: 检索数据中
- `reasoning`: SQL推理中
- `generating`: SQL生成中
- `validating`: SQL验证中
- `finished`: 完成
- `failed`: 失败
- `stopped`: 已停止

#### 7.4.5 VectorDBStatsResponse (向量数据库统计响应)
```python
class VectorDBStatsResponse(BaseModel):
    total_records: int                      # 总记录数
    collection_name: str                    # 集合名称
    embedding_model: str                    # 嵌入模型
    similarity_threshold: float             # 相似度阈值
    avg_feedback_per_record: Optional[float] = None  # 平均反馈数
    helpful_rate: Optional[float] = None    # 有用率
```

**用途**: 提供向量数据库的使用统计信息

### 7.5 工作流状态模型

#### 7.5.1 WorkflowState (工作流状态)
```python
class WorkflowState(BaseModel):
    # 基本信息
    query_id: str                           # 查询ID
    user_query: str                         # 用户问题
    project_id: Optional[str] = None        # 项目ID
    histories: List[AskHistory] = Field(default_factory=list)  # 历史记录
    configuration: Configuration = Field(default_factory=Configuration)  # 配置

    # 额外配置
    mdl_hash: Optional[str] = None          # 模型哈希
    thread_id: Optional[str] = None         # 线程ID
    ignore_sql_generation_reasoning: bool = False  # 跳过推理
    enable_column_pruning: bool = False     # 启用列裁剪

    # Agent执行结果
    intent_result: Optional[IntentResult] = None
    schema_result: Optional[SchemaRetrievalResult] = None
    reasoning_result: Optional[SqlReasoningResult] = None
    generation_result: Optional[SqlGenerationResult] = None
    validation_result: Optional[SqlValidationResult] = None

    # 检索结果
    historical_question_result: Optional[List[dict]] = Field(default_factory=list)
    sql_samples: Optional[List[dict]] = Field(default_factory=list)
    instructions: Optional[List[dict]] = Field(default_factory=list)
    sql_functions: Optional[List[SqlFunction]] = Field(default_factory=list)

    # 错误处理
    retry_count: int = 0                    # 重试次数
    error_messages: List[str] = Field(default_factory=list)  # 错误消息

    # 状态跟踪
    status: Literal["understanding", "searching", "reasoning", "generating", "validating", "finished", "failed"] = "understanding"
```

**用途**: 跟踪整个工作流的执行状态和中间结果

## 8. API接口详细规格

### 8.1 RESTful API设计

系统采用RESTful API设计，所有接口遵循HTTP标准和JSON数据格式。

#### 8.1.1 基础信息
- **基础URL**: `http://localhost:5000`
- **API版本**: `v1`
- **数据格式**: JSON
- **字符编码**: UTF-8

#### 8.1.2 通用响应格式
```json
{
    "status": "success|error",
    "data": {...},
    "message": "操作结果描述",
    "timestamp": "2024-01-01T00:00:00Z"
}
```

### 8.2 核心API端点

#### 8.2.1 提交问题
**端点**: `POST /v1/asks`

**请求头**:
```
Content-Type: application/json
```

**请求体**:
```json
{
    "query": "How many customers do we have?",
    "project_id": "demo_project",
    "configurations": {
        "language": "English"
    }
}
```

**响应**:
```json
{
    "query_id": "123e4567-e89b-12d3-a456-426614174000"
}
```

**状态码**:
- `200`: 成功提交
- `400`: 请求参数错误
- `500`: 服务器内部错误

#### 8.2.2 获取查询结果
**端点**: `GET /v1/asks/{query_id}/result`

**路径参数**:
- `query_id`: 查询ID

**响应示例**:
```json
{
    "status": "finished",
    "response": [
        {
            "sql": "SELECT COUNT(*) FROM customers",
            "type": "llm"
        }
    ],
    "type": "TEXT_TO_SQL",
    "rephrased_question": "How many customers are there in total?",
    "intent_reasoning": "User wants to count the total number of customers",
    "sql_generation_reasoning": "Need to count all records in customers table",
    "retrieved_tables": ["customers"]
}
```

**状态码**:
- `200`: 成功获取结果
- `404`: 查询ID不存在
- `500`: 服务器内部错误

#### 8.2.3 获取查询状态
**端点**: `GET /v1/asks/{query_id}/status`

**响应示例**:
```json
{
    "query_id": "123e4567-e89b-12d3-a456-426614174000",
    "status": "generating",
    "progress": 60,
    "current_step": "SQL生成中"
}
```

#### 8.2.4 停止查询
**端点**: `PATCH /v1/asks/{query_id}`

**请求体**:
```json
{
    "status": "stopped"
}
```

**响应**:
```json
{
    "query_id": "123e4567-e89b-12d3-a456-426614174000"
}
```

#### 8.2.5 提交反馈
**端点**: `POST /v1/asks/{query_id}/feedback`

**请求体**:
```json
{
    "is_helpful": true,
    "comment": "This SQL is exactly what I needed!"
}
```

**响应**:
```json
{
    "success": true,
    "message": "Feedback submitted successfully"
}
```

#### 8.2.6 存储问题-SQL对
**端点**: `POST /v1/vector-db/store`

**请求体**:
```json
{
    "question": "How many orders were placed today?",
    "sql": "SELECT COUNT(*) FROM orders WHERE DATE(order_date) = CURRENT_DATE",
    "project_id": "demo_project",
    "metadata": {
        "category": "daily_report",
        "difficulty": "easy"
    }
}
```

**响应**:
```json
{
    "success": true,
    "doc_id": "doc_123456789",
    "message": "Question-SQL pair stored successfully"
}
```

#### 8.2.7 获取向量数据库统计
**端点**: `GET /v1/vector-db/stats`

**响应**:
```json
{
    "total_records": 150,
    "collection_name": "historical_questions",
    "embedding_model": "all-MiniLM-L6-v2",
    "similarity_threshold": 0.7,
    "avg_feedback_per_record": 2.3,
    "helpful_rate": 0.85
}
```

#### 8.2.8 健康检查
**端点**: `GET /health`

**响应**:
```json
{
    "status": "healthy",
    "service": "wren-ai-flask",
    "version": "1.0.0",
    "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 8.2.9 调试信息 (开发模式)
**端点**: `GET /v1/debug/workflow/{query_id}`

**响应**:
```json
{
    "query_id": "123e4567-e89b-12d3-a456-426614174000",
    "status": "finished",
    "user_query": "How many customers do we have?",
    "intent_result": {...},
    "schema_result": {...},
    "reasoning_result": {...},
    "generation_result": {...},
    "validation_result": {...},
    "retry_count": 0,
    "error_messages": []
}
```

### 8.3 前端页面路由

#### 8.3.1 主页面
**端点**: `GET /`
**功能**: 完整的前端界面，包含智能问答、数据管理、统计信息

#### 8.3.2 演示页面
**端点**: `GET /demo`
**功能**: 简化的演示界面，预设示例问题

### 8.4 错误处理

#### 8.4.1 HTTP状态码
- `200`: 成功
- `400`: 客户端请求错误
- `404`: 资源不存在
- `500`: 服务器内部错误
- `503`: 服务不可用

#### 8.4.2 错误响应格式
```json
{
    "error": {
        "code": "VALIDATION_ERROR",
        "message": "Invalid request parameters",
        "details": {
            "field": "query",
            "reason": "Query cannot be empty"
        }
    },
    "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 8.4.3 常见错误代码
- `VALIDATION_ERROR`: 请求参数验证失败
- `QUERY_NOT_FOUND`: 查询ID不存在
- `SQL_GENERATION_FAILED`: SQL生成失败
- `VECTOR_DB_ERROR`: 向量数据库操作失败
- `LLM_SERVICE_ERROR`: LLM服务调用失败

## 9. 系统集成规格

### 9.1 外部服务集成

#### 9.1.1 DeepSeek LLM集成
**服务类型**: 大语言模型API
**协议**: HTTP/REST
**认证**: API Key

**配置参数**:
```python
DEEPSEEK_API_KEY = "your-api-key"
DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"
DEEPSEEK_MODEL = "deepseek-chat"
```

**调用示例**:
```python
async def call_llm(self, prompt: str, system_prompt: str = "") -> str:
    response = await self.client.chat.completions.create(
        model=self.model_name,
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": prompt}
        ],
        temperature=0.0,
        max_tokens=4000
    )
    return response.choices[0].message.content
```

#### 9.1.2 Wren Engine集成
**服务类型**: SQL验证引擎
**协议**: HTTP/REST
**功能**: SQL语法和执行验证

**配置参数**:
```python
WREN_ENGINE_ENDPOINT = "http://localhost:8080"
ENGINE_TIMEOUT = 30.0
```

**验证流程**:
1. 发送SQL到Wren Engine
2. 执行dry run验证
3. 返回验证结果和错误信息

#### 9.1.3 Chroma向量数据库集成
**服务类型**: 向量数据库
**协议**: 本地API
**功能**: 语义检索和存储

**配置参数**:
```python
CHROMA_PERSIST_DIRECTORY = "./chroma_db"
CHROMA_COLLECTION_NAME = "historical_questions"
EMBEDDING_MODEL = "all-MiniLM-L6-v2"
```

**集合配置**:
```python
collection_metadata = {
    "hnsw:space": "cosine",
    "hnsw:construction_ef": 200,
    "hnsw:M": 16
}
```

### 9.2 数据流集成

#### 9.2.1 输入数据流
```
用户问题 → 前端界面 → Flask API → WorkflowService → Agent执行
```

#### 9.2.2 输出数据流
```
Agent结果 → WorkflowService → Flask API → 前端界面 → 用户
```

#### 9.2.3 反馈数据流
```
用户反馈 → 前端界面 → Flask API → VectorDBService → Chroma数据库
```

### 9.3 缓存集成

#### 9.3.1 内存缓存
**实现**: TTLCache
**用途**: 工作流状态和结果缓存

```python
self.workflow_states = TTLCache(
    maxsize=Config.CACHE_MAXSIZE,  # 1000000
    ttl=Config.CACHE_TTL           # 120秒
)
```

#### 9.3.2 缓存策略
- **工作流状态**: 120秒TTL
- **查询结果**: 120秒TTL
- **LRU淘汰**: 最近最少使用优先淘汰

### 9.4 日志集成

#### 9.4.1 日志配置
```python
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('wren_ai.log'),
        logging.StreamHandler()
    ]
)
```

#### 9.4.2 日志级别
- **DEBUG**: 详细调试信息
- **INFO**: 一般信息记录
- **WARNING**: 警告信息
- **ERROR**: 错误信息
- **CRITICAL**: 严重错误

#### 9.4.3 日志内容
- Agent执行记录
- API请求响应
- 错误堆栈信息
- 性能指标数据

## 10. 性能和监控规格

### 10.1 性能指标

#### 10.1.1 响应时间指标
- **API响应时间**: < 100ms (不包含LLM调用)
- **SQL生成时间**: < 30s (包含重试)
- **向量检索时间**: < 1s
- **前端页面加载**: < 2s

#### 10.1.2 吞吐量指标
- **并发请求**: 支持100个并发查询
- **每日查询量**: 支持10,000次查询
- **向量数据库**: 支持100万条记录

#### 10.1.3 资源使用指标
- **内存使用**: < 2GB
- **CPU使用**: < 80%
- **磁盘空间**: 向量数据库 < 10GB

### 10.2 监控机制

#### 10.2.1 健康检查
- **端点**: `/health`
- **检查频率**: 每30秒
- **检查内容**: 服务状态、数据库连接、外部服务可用性

#### 10.2.2 性能监控
- **工作流执行时间**
- **Agent调用次数**
- **成功率统计**
- **错误率统计**

#### 10.2.3 业务监控
- **查询量统计**
- **用户反馈统计**
- **向量数据库增长**
- **热门问题分析**

### 10.3 容错机制

#### 10.3.1 重试策略
- **SQL生成重试**: 最多3次
- **LLM调用重试**: 指数退避
- **向量数据库重试**: 最多2次

#### 10.3.2 降级策略
- **LLM服务不可用**: 返回缓存结果
- **向量数据库不可用**: 跳过历史检索
- **Wren Engine不可用**: 跳过SQL验证

#### 10.3.3 熔断机制
- **连续失败阈值**: 5次
- **熔断时间**: 60秒
- **半开状态**: 允许1次尝试

## 11. 安全规格

### 11.1 数据安全

#### 11.1.1 敏感数据保护
- **API密钥**: 环境变量存储
- **用户查询**: 不记录敏感信息
- **SQL结果**: 不缓存实际数据

#### 11.1.2 数据传输安全
- **HTTPS**: 生产环境强制使用
- **API认证**: 支持Token认证
- **请求验证**: 严格的输入验证

### 11.2 访问控制

#### 11.2.1 API访问控制
- **项目隔离**: 基于project_id的数据隔离
- **用户权限**: 支持角色基础访问控制
- **请求限制**: API调用频率限制

#### 11.2.2 前端安全
- **XSS防护**: HTML转义处理
- **CSRF防护**: CSRF令牌验证
- **内容安全策略**: CSP头部设置

### 11.3 审计日志

#### 11.3.1 操作审计
- **用户查询记录**
- **反馈操作记录**
- **数据存储操作**
- **系统配置变更**

#### 11.3.2 安全审计
- **异常访问记录**
- **失败认证记录**
- **权限变更记录**
- **敏感操作记录**

## 12. 部署和运维规格

### 12.1 部署要求

#### 12.1.1 系统要求
- **操作系统**: Linux/Windows/macOS
- **Python版本**: 3.8+
- **内存**: 最小2GB，推荐4GB
- **磁盘**: 最小10GB可用空间

#### 12.1.2 依赖服务
- **DeepSeek API**: 外部LLM服务
- **Wren Engine**: SQL验证服务 (可选)
- **Chroma**: 向量数据库 (内嵌)

### 12.2 配置管理

#### 12.2.1 环境配置
- **开发环境**: 本地开发配置
- **测试环境**: 自动化测试配置
- **生产环境**: 生产部署配置

#### 12.2.2 配置文件
- **.env**: 环境变量配置
- **config.py**: 应用配置
- **logging.conf**: 日志配置

### 12.3 运维操作

#### 12.3.1 启动和停止
```bash
# 启动服务
python start.py

# 停止服务
Ctrl+C 或 kill -TERM <pid>
```

#### 12.3.2 数据备份
```bash
# 备份向量数据库
cp -r ./chroma_db ./backup/chroma_db_$(date +%Y%m%d)

# 备份日志文件
cp wren_ai.log ./backup/wren_ai_$(date +%Y%m%d).log
```

#### 12.3.3 监控命令
```bash
# 检查服务状态
curl http://localhost:5000/health

# 查看向量数据库统计
curl http://localhost:5000/v1/vector-db/stats

# 查看日志
tail -f wren_ai.log
```

## 13. 测试规格

### 13.1 测试策略

#### 13.1.1 单元测试
- **Agent功能测试**
- **数据模型验证**
- **工具函数测试**
- **覆盖率要求**: > 80%

#### 13.1.2 集成测试
- **API端点测试**
- **工作流集成测试**
- **外部服务集成测试**

#### 13.1.3 端到端测试
- **完整用户场景测试**
- **前端功能测试**
- **性能压力测试**

### 13.2 测试工具

#### 13.2.1 API测试
```bash
python test_api.py              # 基础API测试
python test_vector_db.py        # 向量数据库测试
```

#### 13.2.2 前端测试
```bash
pip install selenium
python test_frontend.py         # 前端自动化测试
```

#### 13.2.3 性能测试
```bash
python test_performance.py      # 性能基准测试
```

### 13.3 测试数据

#### 13.3.1 示例问题
- "How many customers do we have?"
- "What is the total sales amount?"
- "Show me the top 5 customers by order value"
- "What are the monthly sales trends?"

#### 13.3.2 测试数据库
- **customers表**: 客户信息
- **orders表**: 订单信息
- **order_items表**: 订单明细
- **products表**: 产品信息

---

## 附录

### A. 术语表

- **Agent**: 基于PydanticAI的智能代理
- **Workflow**: 多Agent协作的工作流程
- **Vector DB**: 向量数据库，用于语义检索
- **Embedding**: 文本嵌入向量
- **LLM**: 大语言模型 (Large Language Model)
- **DDL**: 数据定义语言 (Data Definition Language)
- **TTL**: 生存时间 (Time To Live)

### B. 参考资料

- [PydanticAI Documentation](https://ai.pydantic.dev/)
- [DeepSeek API Documentation](https://platform.deepseek.com/api-docs/)
- [Chroma Documentation](https://docs.trychroma.com/)
- [Flask Documentation](https://flask.palletsprojects.com/)

### C. 版本历史

- **v1.0.0**: 初始版本，包含完整的Agent工作流和向量数据库功能
- **v1.1.0**: 添加前端界面和反馈机制
- **v1.2.0**: 性能优化和监控增强

---

*本文档最后更新时间: 2024年*
