#!/usr/bin/env python3
"""
前端功能测试脚本
使用 Selenium 自动化测试前端界面
"""

import time
import sys
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options


class FrontendTester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.driver = None
        
    def setup_driver(self):
        """设置 Chrome 驱动"""
        try:
            chrome_options = Options()
            chrome_options.add_argument("--headless")  # 无头模式
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(10)
            print("✅ Chrome 驱动初始化成功")
            return True
        except Exception as e:
            print(f"❌ Chrome 驱动初始化失败: {e}")
            print("💡 请确保已安装 Chrome 浏览器和 ChromeDriver")
            return False
    
    def test_service_availability(self):
        """测试服务可用性"""
        print("🔍 测试服务可用性...")
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ 服务运行正常")
                return True
            else:
                print(f"❌ 服务响应异常: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 无法连接到服务: {e}")
            return False
    
    def test_main_page_load(self):
        """测试主页加载"""
        print("🏠 测试主页加载...")
        try:
            self.driver.get(self.base_url)
            
            # 等待页面标题加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "h1"))
            )
            
            title = self.driver.find_element(By.TAG_NAME, "h1").text
            if "Wren AI" in title:
                print("✅ 主页加载成功")
                return True
            else:
                print(f"❌ 主页标题异常: {title}")
                return False
                
        except Exception as e:
            print(f"❌ 主页加载失败: {e}")
            return False
    
    def test_demo_page_load(self):
        """测试演示页面加载"""
        print("🎯 测试演示页面加载...")
        try:
            self.driver.get(f"{self.base_url}/demo")
            
            # 等待页面标题加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "h1"))
            )
            
            title = self.driver.find_element(By.TAG_NAME, "h1").text
            if "演示" in title:
                print("✅ 演示页面加载成功")
                return True
            else:
                print(f"❌ 演示页面标题异常: {title}")
                return False
                
        except Exception as e:
            print(f"❌ 演示页面加载失败: {e}")
            return False
    
    def test_tab_switching(self):
        """测试标签页切换"""
        print("📑 测试标签页切换...")
        try:
            self.driver.get(self.base_url)
            
            # 点击数据管理标签
            store_tab = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '[data-tab="storeTab"]'))
            )
            store_tab.click()
            
            # 检查标签页是否激活
            time.sleep(1)
            active_tab = self.driver.find_element(By.CSS_SELECTOR, '.tab.active')
            if "数据管理" in active_tab.text:
                print("✅ 标签页切换成功")
                return True
            else:
                print("❌ 标签页切换失败")
                return False
                
        except Exception as e:
            print(f"❌ 标签页切换测试失败: {e}")
            return False
    
    def test_form_validation(self):
        """测试表单验证"""
        print("📝 测试表单验证...")
        try:
            self.driver.get(self.base_url)
            
            # 尝试提交空表单
            submit_btn = self.driver.find_element(By.CSS_SELECTOR, '#askForm button[type="submit"]')
            submit_btn.click()
            
            # 检查是否有验证提示
            time.sleep(1)
            query_input = self.driver.find_element(By.ID, "userQuery")
            
            # HTML5 验证会阻止表单提交
            if query_input.get_attribute("required"):
                print("✅ 表单验证正常")
                return True
            else:
                print("❌ 表单验证异常")
                return False
                
        except Exception as e:
            print(f"❌ 表单验证测试失败: {e}")
            return False
    
    def test_demo_question_buttons(self):
        """测试演示问题按钮"""
        print("🔘 测试演示问题按钮...")
        try:
            self.driver.get(f"{self.base_url}/demo")
            
            # 点击第一个演示问题按钮
            demo_btn = WebDriverWait(self.driver, 10).until(
                EC.element_to_be_clickable((By.CSS_SELECTOR, '.demo-question'))
            )
            demo_btn.click()
            
            # 检查问题是否填入文本框
            time.sleep(1)
            query_input = self.driver.find_element(By.ID, "demoQuery")
            
            if query_input.get_attribute("value"):
                print("✅ 演示问题按钮功能正常")
                return True
            else:
                print("❌ 演示问题按钮功能异常")
                return False
                
        except Exception as e:
            print(f"❌ 演示问题按钮测试失败: {e}")
            return False
    
    def test_responsive_design(self):
        """测试响应式设计"""
        print("📱 测试响应式设计...")
        try:
            self.driver.get(self.base_url)
            
            # 测试不同屏幕尺寸
            screen_sizes = [
                (1920, 1080),  # 桌面
                (768, 1024),   # 平板
                (375, 667)     # 手机
            ]
            
            for width, height in screen_sizes:
                self.driver.set_window_size(width, height)
                time.sleep(1)
                
                # 检查页面是否正常显示
                container = self.driver.find_element(By.CLASS_NAME, "container")
                if container.is_displayed():
                    print(f"   ✅ {width}x{height} 显示正常")
                else:
                    print(f"   ❌ {width}x{height} 显示异常")
                    return False
            
            print("✅ 响应式设计测试通过")
            return True
            
        except Exception as e:
            print(f"❌ 响应式设计测试失败: {e}")
            return False
    
    def test_javascript_functionality(self):
        """测试 JavaScript 功能"""
        print("⚡ 测试 JavaScript 功能...")
        try:
            self.driver.get(self.base_url)
            
            # 检查 jQuery 是否加载
            jquery_loaded = self.driver.execute_script("return typeof jQuery !== 'undefined'")
            
            if jquery_loaded:
                print("   ✅ jQuery 加载成功")
            else:
                print("   ❌ jQuery 加载失败")
                return False
            
            # 检查自定义 JavaScript 是否加载
            app_loaded = self.driver.execute_script("return typeof window.wrenApp !== 'undefined'")
            
            if app_loaded:
                print("   ✅ 应用 JavaScript 加载成功")
            else:
                print("   ❌ 应用 JavaScript 加载失败")
                return False
            
            print("✅ JavaScript 功能测试通过")
            return True
            
        except Exception as e:
            print(f"❌ JavaScript 功能测试失败: {e}")
            return False
    
    def cleanup(self):
        """清理资源"""
        if self.driver:
            self.driver.quit()
            print("🧹 浏览器驱动已关闭")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始前端功能测试\n")
        
        # 检查服务可用性
        if not self.test_service_availability():
            print("\n💥 服务不可用，停止测试")
            return False
        
        # 设置浏览器驱动
        if not self.setup_driver():
            print("\n💥 浏览器驱动设置失败，停止测试")
            return False
        
        tests = [
            ("主页加载", self.test_main_page_load),
            ("演示页面加载", self.test_demo_page_load),
            ("标签页切换", self.test_tab_switching),
            ("表单验证", self.test_form_validation),
            ("演示问题按钮", self.test_demo_question_buttons),
            ("响应式设计", self.test_responsive_design),
            ("JavaScript功能", self.test_javascript_functionality)
        ]
        
        success_count = 0
        
        for test_name, test_func in tests:
            print(f"\n{'='*50}")
            try:
                if test_func():
                    success_count += 1
            except Exception as e:
                print(f"❌ {test_name} 测试异常: {e}")
        
        print(f"\n{'='*50}")
        print(f"📊 测试总结: {success_count}/{len(tests)} 项测试通过")
        
        self.cleanup()
        
        return success_count == len(tests)


def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"
    
    print(f"测试前端功能，服务地址: {base_url}")
    print("注意：此测试需要安装 Chrome 浏览器和 ChromeDriver")
    print("安装命令: pip install selenium")
    
    tester = FrontendTester(base_url)
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 所有前端测试通过！")
        print(f"🌐 访问地址: {base_url}")
        print(f"🎯 演示页面: {base_url}/demo")
        sys.exit(0)
    else:
        print("\n💥 部分前端测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
