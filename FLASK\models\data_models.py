from typing import Optional, List, Literal
from pydantic import BaseModel, Field


class AskHistory(BaseModel):
    sql: str
    question: str


class Configuration(BaseModel):
    language: Optional[str] = "English"
    fiscal_year: Optional[dict] = None


class Document(BaseModel):
    id: str
    content: str
    meta: dict = Field(default_factory=dict)


class SqlFunction(BaseModel):
    name: str
    description: str
    parameters: List[dict] = Field(default_factory=list)


class IntentResult(BaseModel):
    intent: Literal["SQL_QUERY", "GENERAL", "MISLEADING_QUERY"]
    reasoning: str
    rephrased_question: Optional[str] = None
    confidence: float = 0.0


class SchemaRetrievalResult(BaseModel):
    table_ddls: List[str]
    table_names: List[str]
    has_calculated_field: bool = False
    has_metric: bool = False


class SqlReasoningResult(BaseModel):
    reasoning: str
    step_by_step_plan: List[str]


class SqlGenerationResult(BaseModel):
    sql: str
    correlation_id: Optional[str] = None


class SqlValidationResult(BaseModel):
    is_valid: bool
    error_message: Optional[str] = None
    error_type: Optional[Literal["SYNTAX", "EXECUTION", "TIMEOUT"]] = None


class WorkflowState(BaseModel):
    query_id: str
    user_query: str
    project_id: Optional[str] = None
    histories: List[AskHistory] = Field(default_factory=list)
    configuration: Configuration = Field(default_factory=Configuration)
    
    # 中间结果
    intent_result: Optional[IntentResult] = None
    schema_result: Optional[SchemaRetrievalResult] = None
    reasoning_result: Optional[SqlReasoningResult] = None
    generation_result: Optional[SqlGenerationResult] = None
    validation_result: Optional[SqlValidationResult] = None
    
    # 错误处理
    retry_count: int = 0
    error_messages: List[str] = Field(default_factory=list)
    
    # 状态跟踪
    status: Literal["understanding", "searching", "reasoning", "generating", "validating", "finished", "failed"] = "understanding"
