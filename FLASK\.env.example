# DeepSeek API 配置
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# Flask 配置
SECRET_KEY=your-secret-key-here
DEBUG=True

# 数据库和引擎配置
WREN_ENGINE_ENDPOINT=http://localhost:8080
WREN_IBIS_ENDPOINT=http://localhost:8000

# 缓存配置
CACHE_TTL=120
CACHE_MAXSIZE=1000000

# 重试配置
MAX_SQL_CORRECTION_RETRIES=3
ENGINE_TIMEOUT=30.0

# 检索配置
TABLE_RETRIEVAL_SIZE=10
TABLE_COLUMN_RETRIEVAL_SIZE=20

# Langfuse 配置（可选）
LANGFUSE_PUBLIC_KEY=
LANGFUSE_SECRET_KEY=
LANGFUSE_HOST=
