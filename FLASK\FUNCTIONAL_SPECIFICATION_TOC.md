# Wren AI Flask Service - 功能式样书目录

## 📋 文档结构

### 1. 系统概述
- 1.1 系统简介
- 1.2 核心特性
- 1.3 技术架构
- 1.4 工作流程概述

### 2. 系统配置
- 2.1 环境变量配置
- 2.2 系统依赖

### 3. 核心组件架构
- 3.1 Agent基类设计
- 3.2 工作流服务设计
- 3.3 向量数据库服务

### 4. Agent详细规格
- 4.1 HistoricalQuestionAgent (历史问题检索Agent)
  - 4.1.1 search_similar_questions
  - 4.1.2 store_question_sql_pair
  - 4.1.3 update_feedback
- 4.2 IntentAgent (意图分类Agent)
  - 4.2.1 classify_intent
- 4.3 SchemaRetrievalAgent (表结构检索Agent)
  - 4.3.1 retrieve_schema
- 4.4 SqlReasoningAgent (SQL推理Agent)
  - 4.4.1 generate_reasoning
- 4.5 SqlGenerationAgent (SQL生成Agent)
  - 4.5.1 generate_sql
- 4.6 SqlValidationAgent (SQL验证Agent)
  - 4.6.1 validate_sql
- 4.7 SqlPairsRetrievalAgent (SQL样例检索Agent)
- 4.8 InstructionsRetrievalAgent (指令检索Agent)
- 4.9 SqlFunctionsAgent (SQL函数检索Agent)

### 5. 工作流详细规格
- 5.1 WorkflowService (工作流服务)
- 5.2 核心工作流方法
  - 5.2.1 process_ask_request
  - 5.2.2 _execute_workflow
- 5.3 工作流状态管理
  - 5.3.1 WorkflowState数据结构
  - 5.3.2 状态转换图
- 5.4 详细工作流步骤
  - 5.4.1 步骤1: 历史问题检索
  - 5.4.2 步骤2: 并行资源检索
  - 5.4.3 步骤3: 意图分类
  - 5.4.4 步骤4: 表结构检索
  - 5.4.5 步骤5: SQL推理
  - 5.4.6 步骤6: SQL生成与验证
- 5.5 错误处理机制
  - 5.5.1 重试策略
  - 5.5.2 错误类型处理
- 5.6 工作流分支
  - 5.6.1 SQL查询工作流
  - 5.6.2 通用问题工作流
  - 5.6.3 误导性问题工作流
- 5.7 缓存和性能优化
  - 5.7.1 状态缓存
  - 5.7.2 结果缓存
  - 5.7.3 并行执行优化
- 5.8 监控和日志
  - 5.8.1 状态跟踪
  - 5.8.2 性能指标

### 6. 向量数据库详细规格
- 6.1 VectorDBService (向量数据库服务)
- 6.2 核心功能方法
  - 6.2.1 存储问题-SQL对
  - 6.2.2 搜索相似问题
  - 6.2.3 更新反馈
- 6.3 数据存储结构
  - 6.3.1 Chroma集合配置
  - 6.3.2 文档元数据结构

### 7. 数据模型详细规格
- 7.1 核心数据模型
  - 7.1.1 AskHistory (历史问答记录)
  - 7.1.2 Configuration (配置信息)
  - 7.1.3 SqlFunction (SQL函数)
- 7.2 Agent结果模型
  - 7.2.1 IntentResult (意图分类结果)
  - 7.2.2 SchemaRetrievalResult (表结构检索结果)
  - 7.2.3 SqlReasoningResult (SQL推理结果)
  - 7.2.4 SqlGenerationResult (SQL生成结果)
  - 7.2.5 SqlValidationResult (SQL验证结果)
- 7.3 请求模型
  - 7.3.1 AskRequest (问答请求)
  - 7.3.2 FeedbackRequest (反馈请求)
  - 7.3.3 StoreQuestionSqlRequest (存储请求)
- 7.4 响应模型
  - 7.4.1 AskResponse (问答响应)
  - 7.4.2 AskResult (问答结果)
  - 7.4.3 AskError (错误信息)
  - 7.4.4 AskResultResponse (完整问答响应)
  - 7.4.5 VectorDBStatsResponse (向量数据库统计响应)
- 7.5 工作流状态模型
  - 7.5.1 WorkflowState (工作流状态)

### 8. API接口详细规格
- 8.1 RESTful API设计
  - 8.1.1 基础信息
  - 8.1.2 通用响应格式
- 8.2 核心API端点
  - 8.2.1 提交问题 (POST /v1/asks)
  - 8.2.2 获取查询结果 (GET /v1/asks/{query_id}/result)
  - 8.2.3 获取查询状态 (GET /v1/asks/{query_id}/status)
  - 8.2.4 停止查询 (PATCH /v1/asks/{query_id})
  - 8.2.5 提交反馈 (POST /v1/asks/{query_id}/feedback)
  - 8.2.6 存储问题-SQL对 (POST /v1/vector-db/store)
  - 8.2.7 获取向量数据库统计 (GET /v1/vector-db/stats)
  - 8.2.8 健康检查 (GET /health)
  - 8.2.9 调试信息 (GET /v1/debug/workflow/{query_id})
- 8.3 前端页面路由
  - 8.3.1 主页面 (GET /)
  - 8.3.2 演示页面 (GET /demo)
- 8.4 错误处理
  - 8.4.1 HTTP状态码
  - 8.4.2 错误响应格式
  - 8.4.3 常见错误代码

### 9. 系统集成规格
- 9.1 外部服务集成
  - 9.1.1 DeepSeek LLM集成
  - 9.1.2 Wren Engine集成
  - 9.1.3 Chroma向量数据库集成
- 9.2 数据流集成
  - 9.2.1 输入数据流
  - 9.2.2 输出数据流
  - 9.2.3 反馈数据流
- 9.3 缓存集成
  - 9.3.1 内存缓存
  - 9.3.2 缓存策略
- 9.4 日志集成
  - 9.4.1 日志配置
  - 9.4.2 日志级别
  - 9.4.3 日志内容

### 10. 性能和监控规格
- 10.1 性能指标
  - 10.1.1 响应时间指标
  - 10.1.2 吞吐量指标
  - 10.1.3 资源使用指标
- 10.2 监控机制
  - 10.2.1 健康检查
  - 10.2.2 性能监控
  - 10.2.3 业务监控
- 10.3 容错机制
  - 10.3.1 重试策略
  - 10.3.2 降级策略
  - 10.3.3 熔断机制

### 11. 安全规格
- 11.1 数据安全
  - 11.1.1 敏感数据保护
  - 11.1.2 数据传输安全
- 11.2 访问控制
  - 11.2.1 API访问控制
  - 11.2.2 前端安全
- 11.3 审计日志
  - 11.3.1 操作审计
  - 11.3.2 安全审计

### 12. 部署和运维规格
- 12.1 部署要求
  - 12.1.1 系统要求
  - 12.1.2 依赖服务
- 12.2 配置管理
  - 12.2.1 环境配置
  - 12.2.2 配置文件
- 12.3 运维操作
  - 12.3.1 启动和停止
  - 12.3.2 数据备份
  - 12.3.3 监控命令

### 13. 测试规格
- 13.1 测试策略
  - 13.1.1 单元测试
  - 13.1.2 集成测试
  - 13.1.3 端到端测试
- 13.2 测试工具
  - 13.2.1 API测试
  - 13.2.2 前端测试
  - 13.2.3 性能测试
- 13.3 测试数据
  - 13.3.1 示例问题
  - 13.3.2 测试数据库

### 附录
- A. 术语表
- B. 参考资料
- C. 版本历史

---

## 📊 关键指标总览

### 技术栈
- **Agent框架**: PydanticAI 0.0.14
- **LLM模型**: DeepSeek Chat
- **向量数据库**: Chroma 0.4.22
- **Web框架**: Flask 3.0.0
- **前端技术**: jQuery + HTML + CSS

### 核心Agent
- **HistoricalQuestionAgent**: 历史问题检索
- **IntentAgent**: 意图分类
- **SchemaRetrievalAgent**: 表结构检索
- **SqlReasoningAgent**: SQL推理
- **SqlGenerationAgent**: SQL生成
- **SqlValidationAgent**: SQL验证

### API端点
- **9个核心API**: 问答、反馈、存储、统计等
- **2个前端页面**: 主页面和演示页面
- **RESTful设计**: 标准HTTP方法和状态码

### 性能目标
- **API响应**: < 100ms
- **SQL生成**: < 30s
- **向量检索**: < 1s
- **并发支持**: 100个查询
- **数据容量**: 100万条记录

### 安全特性
- **数据隔离**: 基于project_id
- **输入验证**: 严格的参数检查
- **错误处理**: 完整的异常捕获
- **日志审计**: 详细的操作记录

---

*目录最后更新时间: 2024年*
