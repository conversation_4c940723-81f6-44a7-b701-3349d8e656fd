import logging
from typing import List, Optional
from .base_agent import BaseAgent
from models.data_models import SqlFunction

logger = logging.getLogger(__name__)


class SqlFunctionsAgent(BaseAgent):
    """SQL 函数检索 Agent"""
    
    def __init__(self):
        super().__init__()
        
    async def retrieve_sql_functions(
        self,
        query: str,
        project_id: Optional[str] = None
    ) -> List[SqlFunction]:
        """检索 SQL 函数"""
        
        try:
            self.log_agent_action("retrieve_sql_functions", {
                "query": query,
                "project_id": project_id
            })
            
            # 示例 SQL 函数
            sample_functions = [
                SqlFunction(
                    name="COUNT",
                    description="Returns the number of rows in a table or the number of non-null values in a column",
                    parameters=[
                        {"name": "column", "type": "column", "description": "Column to count (optional)"}
                    ]
                ),
                SqlFunction(
                    name="SUM",
                    description="Returns the sum of all values in a numeric column",
                    parameters=[
                        {"name": "column", "type": "numeric", "description": "Numeric column to sum"}
                    ]
                ),
                SqlFunction(
                    name="AVG",
                    description="Returns the average value of a numeric column",
                    parameters=[
                        {"name": "column", "type": "numeric", "description": "Numeric column to average"}
                    ]
                ),
                SqlFunction(
                    name="MAX",
                    description="Returns the maximum value in a column",
                    parameters=[
                        {"name": "column", "type": "any", "description": "Column to find maximum value"}
                    ]
                ),
                SqlFunction(
                    name="MIN",
                    description="Returns the minimum value in a column",
                    parameters=[
                        {"name": "column", "type": "any", "description": "Column to find minimum value"}
                    ]
                ),
                SqlFunction(
                    name="CONCAT",
                    description="Concatenates two or more strings",
                    parameters=[
                        {"name": "string1", "type": "string", "description": "First string"},
                        {"name": "string2", "type": "string", "description": "Second string"},
                        {"name": "...", "type": "string", "description": "Additional strings (optional)"}
                    ]
                ),
                SqlFunction(
                    name="SUBSTRING",
                    description="Extracts a substring from a string",
                    parameters=[
                        {"name": "string", "type": "string", "description": "Source string"},
                        {"name": "start", "type": "integer", "description": "Starting position"},
                        {"name": "length", "type": "integer", "description": "Length of substring (optional)"}
                    ]
                ),
                SqlFunction(
                    name="DATE_TRUNC",
                    description="Truncates a date to a specified precision",
                    parameters=[
                        {"name": "precision", "type": "string", "description": "Precision (year, month, day, etc.)"},
                        {"name": "date", "type": "date", "description": "Date to truncate"}
                    ]
                ),
                SqlFunction(
                    name="EXTRACT",
                    description="Extracts a part from a date/time value",
                    parameters=[
                        {"name": "part", "type": "string", "description": "Part to extract (year, month, day, etc.)"},
                        {"name": "date", "type": "date", "description": "Date to extract from"}
                    ]
                ),
                SqlFunction(
                    name="CASE",
                    description="Conditional expression that returns different values based on conditions",
                    parameters=[
                        {"name": "condition", "type": "boolean", "description": "Condition to evaluate"},
                        {"name": "value_if_true", "type": "any", "description": "Value if condition is true"},
                        {"name": "value_if_false", "type": "any", "description": "Value if condition is false"}
                    ]
                )
            ]
            
            # 根据查询内容进行匹配
            query_lower = query.lower()
            relevant_functions = []
            
            # 关键词匹配逻辑
            function_keywords = {
                "count": ["count", "number", "how many"],
                "sum": ["sum", "total", "add"],
                "avg": ["average", "avg", "mean"],
                "max": ["maximum", "max", "highest", "largest"],
                "min": ["minimum", "min", "lowest", "smallest"],
                "concat": ["concatenate", "concat", "combine", "join"],
                "substring": ["substring", "substr", "extract"],
                "date_trunc": ["truncate", "trunc", "group by date"],
                "extract": ["extract", "get", "part of date"],
                "case": ["case", "when", "if", "conditional"]
            }
            
            for func in sample_functions:
                func_name_lower = func.name.lower()
                if func_name_lower in function_keywords:
                    keywords = function_keywords[func_name_lower]
                    if any(keyword in query_lower for keyword in keywords):
                        relevant_functions.append(func)
            
            # 如果没有匹配的函数，返回常用函数
            if not relevant_functions:
                relevant_functions = sample_functions[:5]  # 返回前5个常用函数
            
            # 限制返回数量
            result = relevant_functions[:10]
            
            self.log_agent_action("sql_functions_retrieved", {
                "found_count": len(result)
            })
            
            return result
            
        except Exception as e:
            logger.error(f"SQL functions retrieval failed: {e}")
            return []
