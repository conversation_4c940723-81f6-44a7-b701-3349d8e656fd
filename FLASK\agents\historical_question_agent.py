import logging
from typing import List, Optional
from .base_agent import BaseAgent

logger = logging.getLogger(__name__)


class HistoricalQuestionAgent(BaseAgent):
    """历史问题检索 Agent"""
    
    def __init__(self):
        super().__init__()
        
    async def retrieve_historical_questions(
        self,
        query: str,
        project_id: Optional[str] = None
    ) -> List[dict]:
        """检索历史问题"""
        
        try:
            self.log_agent_action("retrieve_historical_questions", {
                "query": query,
                "project_id": project_id
            })
            
            # 模拟历史问题检索逻辑
            # 在实际实现中，这里应该调用向量数据库或其他检索服务
            
            # 示例历史问题
            sample_historical_questions = [
                {
                    "statement": "SELECT COUNT(*) FROM customers WHERE created_at >= '2023-01-01'",
                    "question": "How many customers were created this year?",
                    "viewId": None,
                    "similarity": 0.85
                },
                {
                    "statement": "SELECT SUM(total_amount) FROM orders WHERE order_date >= '2023-01-01'",
                    "question": "What is the total sales amount for this year?",
                    "viewId": None,
                    "similarity": 0.78
                },
                {
                    "statement": "SELECT customer_name, COUNT(*) as order_count FROM customers c JOIN orders o ON c.customer_id = o.customer_id GROUP BY c.customer_id, c.customer_name ORDER BY order_count DESC LIMIT 10",
                    "question": "Who are the top 10 customers by number of orders?",
                    "viewId": "view_top_customers",
                    "similarity": 0.72
                }
            ]
            
            # 根据查询内容进行相似度匹配
            query_lower = query.lower()
            relevant_questions = []
            
            for question in sample_historical_questions:
                question_lower = question["question"].lower()
                
                # 简单的关键词匹配逻辑
                if any(word in question_lower for word in query_lower.split()):
                    relevant_questions.append(question)
            
            # 如果没有匹配的历史问题，返回空列表
            if not relevant_questions:
                relevant_questions = []
            
            # 按相似度排序，只返回最相关的一个
            relevant_questions.sort(key=lambda x: x.get("similarity", 0), reverse=True)
            result = relevant_questions[:1]  # 只返回最相关的一个
            
            self.log_agent_action("historical_questions_retrieved", {
                "found_count": len(result),
                "has_view": any(q.get("viewId") for q in result)
            })
            
            return result
            
        except Exception as e:
            logger.error(f"Historical question retrieval failed: {e}")
            return []


class SqlPairsRetrievalAgent(BaseAgent):
    """SQL 样例检索 Agent"""
    
    def __init__(self):
        super().__init__()
        
    async def retrieve_sql_pairs(
        self,
        query: str,
        project_id: Optional[str] = None
    ) -> List[dict]:
        """检索 SQL 样例"""
        
        try:
            self.log_agent_action("retrieve_sql_pairs", {
                "query": query,
                "project_id": project_id
            })
            
            # 示例 SQL 样例
            sample_sql_pairs = [
                {
                    "question": "How many records are in the customers table?",
                    "sql": "SELECT COUNT(*) FROM customers",
                    "context": ["customers"]
                },
                {
                    "question": "What is the total sales amount?",
                    "sql": "SELECT SUM(total_amount) FROM orders",
                    "context": ["orders"]
                },
                {
                    "question": "Show me the top customers by order value",
                    "sql": "SELECT c.customer_name, SUM(o.total_amount) as total_sales FROM customers c JOIN orders o ON c.customer_id = o.customer_id GROUP BY c.customer_id, c.customer_name ORDER BY total_sales DESC LIMIT 10",
                    "context": ["customers", "orders"]
                },
                {
                    "question": "What are the most popular products?",
                    "sql": "SELECT product_name, SUM(quantity) as total_quantity FROM order_items GROUP BY product_name ORDER BY total_quantity DESC LIMIT 5",
                    "context": ["order_items"]
                }
            ]
            
            # 根据查询内容进行匹配
            query_lower = query.lower()
            relevant_pairs = []
            
            for pair in sample_sql_pairs:
                question_lower = pair["question"].lower()
                
                # 关键词匹配
                if any(word in question_lower for word in query_lower.split()):
                    relevant_pairs.append(pair)
            
            # 限制返回数量
            result = relevant_pairs[:3]
            
            self.log_agent_action("sql_pairs_retrieved", {
                "found_count": len(result)
            })
            
            return result
            
        except Exception as e:
            logger.error(f"SQL pairs retrieval failed: {e}")
            return []


class InstructionsRetrievalAgent(BaseAgent):
    """指令检索 Agent"""
    
    def __init__(self):
        super().__init__()
        
    async def retrieve_instructions(
        self,
        query: str,
        project_id: Optional[str] = None
    ) -> List[dict]:
        """检索指令"""
        
        try:
            self.log_agent_action("retrieve_instructions", {
                "query": query,
                "project_id": project_id
            })
            
            # 示例指令
            sample_instructions = [
                {
                    "instruction": "Always use proper table aliases for better readability",
                    "is_default": True
                },
                {
                    "instruction": "When calculating totals, always handle NULL values appropriately",
                    "is_default": True
                },
                {
                    "instruction": "For date-related queries, use ISO format (YYYY-MM-DD)",
                    "is_default": True
                },
                {
                    "instruction": "When joining tables, always specify the join condition explicitly",
                    "is_default": False,
                    "question": "How to join tables properly?"
                }
            ]
            
            # 返回默认指令和相关指令
            query_lower = query.lower()
            relevant_instructions = []
            
            for instruction in sample_instructions:
                if instruction.get("is_default", False):
                    relevant_instructions.append(instruction)
                elif "question" in instruction:
                    question_lower = instruction["question"].lower()
                    if any(word in question_lower for word in query_lower.split()):
                        relevant_instructions.append(instruction)
            
            self.log_agent_action("instructions_retrieved", {
                "found_count": len(relevant_instructions)
            })
            
            return relevant_instructions
            
        except Exception as e:
            logger.error(f"Instructions retrieval failed: {e}")
            return []
