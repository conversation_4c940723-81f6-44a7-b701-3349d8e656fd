import logging
from typing import List, Optional
from .base_agent import BaseAgent
from services.vector_db_service import VectorDBService

logger = logging.getLogger(__name__)


class HistoricalQuestionAgent(BaseAgent):
    """历史问题检索 Agent - 基于 Chroma 向量数据库"""

    def __init__(self):
        super().__init__()
        self.vector_db = VectorDBService()

    async def retrieve_historical_questions(
        self,
        query: str,
        project_id: Optional[str] = None
    ) -> List[dict]:
        """检索历史问题"""

        try:
            self.log_agent_action("retrieve_historical_questions", {
                "query": query,
                "project_id": project_id
            })

            # 使用向量数据库搜索相似问题
            similar_questions = await self.vector_db.search_similar_questions(
                question=query,
                project_id=project_id
            )

            self.log_agent_action("historical_questions_retrieved", {
                "found_count": len(similar_questions),
                "has_view": any(q.get("viewId") for q in similar_questions),
                "similarities": [q.get("similarity", 0) for q in similar_questions]
            })

            return similar_questions

        except Exception as e:
            logger.error(f"Historical question retrieval failed: {e}")
            return []

    async def store_question_sql_pair(
        self,
        question: str,
        sql: str,
        project_id: Optional[str] = None,
        view_id: Optional[str] = None,
        metadata: Optional[dict] = None
    ) -> str:
        """存储问题-SQL对"""

        try:
            self.log_agent_action("store_question_sql_pair", {
                "question": question[:100] + "..." if len(question) > 100 else question,
                "sql_length": len(sql),
                "project_id": project_id,
                "view_id": view_id
            })

            doc_id = await self.vector_db.store_question_sql_pair(
                question=question,
                sql=sql,
                project_id=project_id,
                view_id=view_id,
                metadata=metadata
            )

            self.log_agent_action("question_sql_pair_stored", {
                "doc_id": doc_id
            })

            return doc_id

        except Exception as e:
            logger.error(f"Failed to store question-SQL pair: {e}")
            raise

    async def update_feedback(
        self,
        question: str,
        is_helpful: bool,
        project_id: Optional[str] = None
    ) -> bool:
        """更新反馈"""

        try:
            self.log_agent_action("update_feedback", {
                "question": question[:100] + "..." if len(question) > 100 else question,
                "is_helpful": is_helpful,
                "project_id": project_id
            })

            success = await self.vector_db.update_feedback(
                question=question,
                is_helpful=is_helpful,
                project_id=project_id
            )

            self.log_agent_action("feedback_updated", {
                "success": success
            })

            return success

        except Exception as e:
            logger.error(f"Failed to update feedback: {e}")
            return False

    async def get_collection_stats(self) -> dict:
        """获取集合统计信息"""

        try:
            stats = await self.vector_db.get_collection_stats()

            self.log_agent_action("collection_stats_retrieved", {
                "total_records": stats.get("total_records", 0)
            })

            return stats

        except Exception as e:
            logger.error(f"Failed to get collection stats: {e}")
            return {"error": str(e)}


class SqlPairsRetrievalAgent(BaseAgent):
    """SQL 样例检索 Agent"""

    def __init__(self):
        super().__init__()

    async def retrieve_sql_pairs(
        self,
        query: str,
        project_id: Optional[str] = None
    ) -> List[dict]:
        """检索 SQL 样例"""

        try:
            self.log_agent_action("retrieve_sql_pairs", {
                "query": query,
                "project_id": project_id
            })

            # 示例 SQL 样例
            sample_sql_pairs = [
                {
                    "question": "How many records are in the customers table?",
                    "sql": "SELECT COUNT(*) FROM customers",
                    "context": ["customers"]
                },
                {
                    "question": "What is the total sales amount?",
                    "sql": "SELECT SUM(total_amount) FROM orders",
                    "context": ["orders"]
                },
                {
                    "question": "Show me the top customers by order value",
                    "sql": "SELECT c.customer_name, SUM(o.total_amount) as total_sales FROM customers c JOIN orders o ON c.customer_id = o.customer_id GROUP BY c.customer_id, c.customer_name ORDER BY total_sales DESC LIMIT 10",
                    "context": ["customers", "orders"]
                },
                {
                    "question": "What are the most popular products?",
                    "sql": "SELECT product_name, SUM(quantity) as total_quantity FROM order_items GROUP BY product_name ORDER BY total_quantity DESC LIMIT 5",
                    "context": ["order_items"]
                }
            ]

            # 根据查询内容进行匹配
            query_lower = query.lower()
            relevant_pairs = []

            for pair in sample_sql_pairs:
                question_lower = pair["question"].lower()

                # 关键词匹配
                if any(word in question_lower for word in query_lower.split()):
                    relevant_pairs.append(pair)

            # 限制返回数量
            result = relevant_pairs[:3]

            self.log_agent_action("sql_pairs_retrieved", {
                "found_count": len(result)
            })

            return result

        except Exception as e:
            logger.error(f"SQL pairs retrieval failed: {e}")
            return []


class 。InstructionsRetrievalAgent(BaseAgent):
    """指令检索 Agent"""

    def __init__(self):
        super().__init__()

    async def retrieve_instructions(
        self,
        query: str,
        project_id: Optional[str] = None
    ) -> List[dict]:
        """检索指令"""

        try:
            self.log_agent_action("retrieve_instructions", {
                "query": query,
                "project_id": project_id
            })

            # 示例指令
            sample_instructions = [
                {
                    "instruction": "Always use proper table aliases for better readability",
                    "is_default": True
                },
                {
                    "instruction": "When calculating totals, always handle NULL values appropriately",
                    "is_default": True
                },
                {
                    "instruction": "For date-related queries, use ISO format (YYYY-MM-DD)",
                    "is_default": True
                },
                {
                    "instruction": "When joining tables, always specify the join condition explicitly",
                    "is_default": False,
                    "question": "How to join tables properly?"
                }
            ]

            # 返回默认指令和相关指令
            query_lower = query.lower()
            relevant_instructions = []

            for instruction in sample_instructions:
                if instruction.get("is_default", False):
                    relevant_instructions.append(instruction)
                elif "question" in instruction:
                    question_lower = instruction["question"].lower()
                    if any(word in question_lower for word in query_lower.split()):
                        relevant_instructions.append(instruction)

            self.log_agent_action("instructions_retrieved", {
                "found_count": len(relevant_instructions)
            })

            return relevant_instructions

        except Exception as e:
            logger.error(f"Instructions retrieval failed: {e}")
            return []
