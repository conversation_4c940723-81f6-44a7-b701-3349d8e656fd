type: llm
provider: openai_llm
models:
  - model: gpt-4o-mini
    kwargs:
      {
        "temperature": 0,
        "n": 1,
        "max_tokens": 4096,
        "response_format": { "type": "json_object" },
      }
api_base: https://api.openai.com/v1

---
type: embedder
provider: openai_embedder
models:
  - model: text-embedding-3-large
    dimension: 3072
api_base: https://api.openai.com/v1
timeout: 120

---
type: engine
provider: wren_ui
endpoint: http://localhost:3000

---
type: document_store
provider: qdrant
location: http://localhost:6333
embedding_model_dim: 3072
timeout: 120

---
type: pipeline
pipes:
  - name: indexing
    embedder: openai_embedder.text-embedding-3-large
    document_store: qdrant
  - name: retrieval
    llm: openai_llm.gpt-4o-mini
    embedder: openai_embedder.text-embedding-3-large
    document_store: qdrant
  - name: historical_question_retrieval
    embedder: openai_embedder.text-embedding-3-large
    document_store: qdrant
  - name: sql_generation
    llm: openai_llm.gpt-4o-mini
    engine: wren_ui
  - name: sql_correction
    llm: openai_llm.gpt-4o-mini
    engine: wren_ui
  - name: followup_sql_generation
    llm: openai_llm.gpt-4o-mini
    engine: wren_ui
  - name: sql_answer
    llm: openai_llm.gpt-4o-mini
    engine: wren_ui
  - name: sql_explanation
    llm: openai_llm.gpt-4o-mini
  - name: sql_regeneration
    llm: openai_llm.gpt-4o-mini
    engine: wren_ui
  - name: semantics_description
    llm: openai_llm.gpt-4o-mini
  - name: relationship_recommendation
    llm: openai_llm.gpt-4o-mini
    engine: wren_ui
  - name: user_guide_assistance
    llm: openai_llm.gpt-4o-mini
  - name: data_assistance
    llm: openai_llm.gpt-4o-mini
    
---
settings:
  host: 127.0.0.1
  port: 5556
  column_indexing_batch_size: 50
  doc_endpoint: https://docs.getwren.ai
  is_oss: true
  table_retrieval_size: 10
  table_column_retrieval_size: 1000
  query_cache_maxsize: 1000
  query_cache_ttl: 3600
  langfuse_host: https://cloud.langfuse.com
  langfuse_enable: false
  logging_level: INFO
  development: false
