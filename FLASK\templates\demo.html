<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Wren AI - 演示页面</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <div class="header">
            <h1>🎯 Wren AI 演示</h1>
            <p>体验智能SQL生成和向量数据库检索功能</p>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <div class="section">
                <h2>🚀 快速开始</h2>
                <p>以下是一些示例问题，您可以直接点击尝试：</p>
                
                <div class="demo-questions">
                    <button class="btn btn-primary demo-question" data-question="How many customers do we have?">
                        👥 客户数量查询
                    </button>
                    <button class="btn btn-primary demo-question" data-question="What is the total sales amount?">
                        💰 销售总额查询
                    </button>
                    <button class="btn btn-primary demo-question" data-question="Show me the top 5 customers by order value">
                        🏆 顶级客户排名
                    </button>
                    <button class="btn btn-primary demo-question" data-question="What are the monthly sales trends?">
                        📈 月度销售趋势
                    </button>
                </div>
            </div>

            <div class="section">
                <h2>💬 智能问答</h2>
                <form id="demoForm">
                    <div class="form-group">
                        <label for="demoQuery">您的问题</label>
                        <textarea id="demoQuery" class="form-control" 
                            placeholder="输入您的问题，或点击上方的示例问题..."
                            rows="3"></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        🚀 提交问题
                    </button>
                    
                    <a href="/" class="btn btn-secondary">
                        🏠 返回主页
                    </a>
                </form>

                <!-- 加载状态 -->
                <div id="demoLoading" class="loading">
                    <div class="spinner"></div>
                    <p id="demoLoadingMessage">处理中...</p>
                    <div id="demoStatus"></div>
                </div>

                <!-- 查询结果 -->
                <div id="demoResult"></div>
            </div>

            <div class="section">
                <h2>📊 系统特性</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-value">🧠</div>
                        <div class="stat-label">智能意图识别</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">🔍</div>
                        <div class="stat-label">历史问题检索</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">⚡</div>
                        <div class="stat-label">快速SQL生成</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">✅</div>
                        <div class="stat-label">自动SQL验证</div>
                    </div>
                </div>
            </div>

            <div class="section">
                <h2>🔄 工作流程</h2>
                <div class="workflow-steps">
                    <div class="workflow-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>历史检索</h4>
                            <p>在向量数据库中搜索相似的历史问题</p>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>意图分类</h4>
                            <p>分析问题意图：SQL查询、通用问题或误导性问题</p>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>智能生成</h4>
                            <p>表结构检索 → SQL推理 → SQL生成 → SQL验证</p>
                        </div>
                    </div>
                    <div class="workflow-step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h4>自动学习</h4>
                            <p>成功的SQL自动存储，用户反馈持续优化</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .demo-questions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .demo-question {
            padding: 15px;
            text-align: left;
            white-space: normal;
            height: auto;
            min-height: 60px;
        }

        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .workflow-step {
            display: flex;
            align-items: flex-start;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border: 1px solid #e1e5e9;
        }

        .step-number {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .step-content h4 {
            margin-bottom: 8px;
            color: #333;
        }

        .step-content p {
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
    </style>

    <script>
        class WrenAIDemo {
            constructor() {
                this.baseUrl = window.location.origin;
                this.currentQueryId = null;
                this.pollInterval = null;
                this.init();
            }

            init() {
                this.bindEvents();
            }

            bindEvents() {
                // 演示问题按钮
                $('.demo-question').on('click', (e) => {
                    const question = $(e.target).data('question');
                    $('#demoQuery').val(question);
                });

                // 提交表单
                $('#demoForm').on('submit', (e) => {
                    e.preventDefault();
                    this.submitQuestion();
                });
            }

            async submitQuestion() {
                const query = $('#demoQuery').val().trim();

                if (!query) {
                    alert('请输入问题');
                    return;
                }

                const requestData = {
                    query: query,
                    project_id: "demo_project",
                    configurations: {
                        language: "English"
                    }
                };

                try {
                    this.showLoading('正在处理您的问题...');
                    this.disableForm(true);

                    const response = await $.ajax({
                        url: `${this.baseUrl}/v1/asks`,
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify(requestData)
                    });

                    this.currentQueryId = response.query_id;
                    this.startPolling();

                } catch (error) {
                    this.hideLoading();
                    this.disableForm(false);
                    alert(`提交失败: ${error.responseJSON?.error || error.statusText}`);
                }
            }

            startPolling() {
                if (this.pollInterval) {
                    clearInterval(this.pollInterval);
                }

                this.pollInterval = setInterval(() => {
                    this.checkResult();
                }, 2000);

                setTimeout(() => {
                    if (this.pollInterval) {
                        clearInterval(this.pollInterval);
                        this.hideLoading();
                        this.disableForm(false);
                        alert('查询超时');
                    }
                }, 60000);
            }

            async checkResult() {
                if (!this.currentQueryId) return;

                try {
                    const response = await $.ajax({
                        url: `${this.baseUrl}/v1/asks/${this.currentQueryId}/result`,
                        method: 'GET'
                    });

                    this.updateStatus(response.status);

                    if (response.status === 'finished') {
                        clearInterval(this.pollInterval);
                        this.hideLoading();
                        this.disableForm(false);
                        this.displayResult(response);
                    } else if (response.status === 'failed') {
                        clearInterval(this.pollInterval);
                        this.hideLoading();
                        this.disableForm(false);
                        this.displayError(response);
                    }

                } catch (error) {
                    console.error('轮询错误:', error);
                }
            }

            updateStatus(status) {
                const statusMap = {
                    'understanding': '🧠 理解问题',
                    'searching': '🔍 检索数据',
                    'reasoning': '💭 SQL推理',
                    'generating': '⚡ 生成SQL',
                    'validating': '✅ 验证SQL',
                    'finished': '🎉 完成',
                    'failed': '❌ 失败'
                };
                $('#demoStatus').html(`<p>${statusMap[status] || status}</p>`);
            }

            displayResult(result) {
                let html = '<div class="result-container success">';
                html += '<h4>🎉 查询成功</h4>';

                if (result.response && result.response.length > 0) {
                    result.response.forEach((resp, index) => {
                        html += `
                            <div class="sql-result">
                                <h5>SQL ${index + 1} (${resp.type})</h5>
                                <div class="sql-code">${this.escapeHtml(resp.sql)}</div>
                            </div>
                        `;
                    });
                }

                html += '<p><a href="/" class="btn btn-primary">🏠 返回主页体验完整功能</a></p>';
                html += '</div>';
                $('#demoResult').html(html);
            }

            displayError(result) {
                let html = '<div class="result-container error">';
                html += '<h4>❌ 查询失败</h4>';
                if (result.error) {
                    html += `<p><strong>错误:</strong> ${this.escapeHtml(result.error.message)}</p>`;
                }
                html += '</div>';
                $('#demoResult').html(html);
            }

            showLoading(message) {
                $('#demoLoadingMessage').text(message);
                $('#demoLoading').show();
            }

            hideLoading() {
                $('#demoLoading').hide();
            }

            disableForm(disabled) {
                $('#demoForm input, #demoForm textarea, #demoForm button').prop('disabled', disabled);
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        $(document).ready(() => {
            new WrenAIDemo();
        });
    </script>
</body>
</html>
