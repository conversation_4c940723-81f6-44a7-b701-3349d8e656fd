import logging
import uuid
from typing import Dict, Optional
from cachetools import TTL<PERSON>ache
from config import Config
from models.data_models import WorkflowState, IntentResult, AskHistory, Configuration
from models.response_models import AskResultResponse, AskResult, AskError
from agents import (
    IntentAgent, 
    SchemaRetrievalAgent, 
    SqlReasoningAgent, 
    SqlGenerationAgent, 
    SqlValidationAgent
)

logger = logging.getLogger(__name__)


class WorkflowService:
    """工作流服务 - 协调各个 Agent 的执行"""
    
    def __init__(self):
        # 初始化所有 Agent
        self.intent_agent = IntentAgent()
        self.schema_agent = SchemaRetrievalAgent()
        self.reasoning_agent = SqlReasoningAgent()
        self.generation_agent = SqlGenerationAgent()
        self.validation_agent = SqlValidationAgent()
        
        # 结果缓存
        self.workflow_states: Dict[str, WorkflowState] = TTLCache(
            maxsize=Config.CACHE_MAXSIZE,
            ttl=Config.CACHE_TTL
        )
        
        self.ask_results: Dict[str, AskResultResponse] = TTLCache(
            maxsize=Config.CACHE_MAXSIZE,
            ttl=Config.CACHE_TTL
        )
    
    async def process_ask_request(
        self,
        query: str,
        project_id: Optional[str] = None,
        mdl_hash: Optional[str] = None,
        thread_id: Optional[str] = None,
        histories: list[AskHistory] = None,
        configurations: Configuration = None,
        ignore_sql_generation_reasoning: bool = False,
        enable_column_pruning: bool = False
    ) -> str:
        """处理用户问题请求"""
        
        query_id = str(uuid.uuid4())
        
        # 初始化工作流状态
        state = WorkflowState(
            query_id=query_id,
            user_query=query,
            project_id=project_id,
            histories=histories or [],
            configuration=configurations or Configuration(),
            status="understanding"
        )
        
        self.workflow_states[query_id] = state
        self.ask_results[query_id] = AskResultResponse(status="understanding")
        
        # 异步执行工作流
        import asyncio
        asyncio.create_task(self._execute_workflow(query_id))
        
        return query_id
    
    async def _execute_workflow(self, query_id: str):
        """执行完整的工作流"""
        
        try:
            state = self.workflow_states[query_id]
            
            # 步骤1: 意图分类
            await self._step_intent_classification(state)
            
            # 根据意图决定后续流程
            if state.intent_result.intent == "SQL_QUERY":
                await self._execute_sql_workflow(state)
            elif state.intent_result.intent == "GENERAL":
                await self._execute_general_workflow(state)
            else:  # MISLEADING_QUERY
                await self._execute_misleading_workflow(state)
                
        except Exception as e:
            logger.error(f"Workflow execution failed for {query_id}: {e}")
            await self._handle_workflow_error(query_id, str(e))
    
    async def _step_intent_classification(self, state: WorkflowState):
        """步骤1: 意图分类"""
        
        state.status = "understanding"
        self._update_ask_result(state.query_id, "understanding")
        
        # 调用意图分类 Agent
        intent_result = await self.intent_agent.classify_intent(
            query=state.user_query,
            histories=state.histories,
            project_id=state.project_id,
            configuration=state.configuration
        )
        
        state.intent_result = intent_result
        
        # 如果有重新表述的问题，更新查询
        if intent_result.rephrased_question:
            state.user_query = intent_result.rephrased_question
    
    async def _execute_sql_workflow(self, state: WorkflowState):
        """执行 SQL 查询工作流"""
        
        # 步骤2: 表结构检索
        await self._step_schema_retrieval(state)
        
        # 步骤3: SQL 推理
        await self._step_sql_reasoning(state)
        
        # 步骤4: SQL 生成（带重试机制）
        await self._step_sql_generation_with_retry(state)
    
    async def _step_schema_retrieval(self, state: WorkflowState):
        """步骤2: 表结构检索"""
        
        state.status = "searching"
        self._update_ask_result(state.query_id, "searching")
        
        schema_result = await self.schema_agent.retrieve_schema(
            query=state.user_query,
            project_id=state.project_id
        )
        
        state.schema_result = schema_result
    
    async def _step_sql_reasoning(self, state: WorkflowState):
        """步骤3: SQL 推理"""
        
        state.status = "reasoning"
        self._update_ask_result(state.query_id, "reasoning")
        
        reasoning_result = await self.reasoning_agent.generate_reasoning(
            query=state.user_query,
            table_ddls=state.schema_result.table_ddls,
            histories=state.histories,
            configuration=state.configuration
        )
        
        state.reasoning_result = reasoning_result
    
    async def _step_sql_generation_with_retry(self, state: WorkflowState):
        """步骤4: SQL 生成（带重试机制）"""
        
        max_retries = Config.MAX_SQL_CORRECTION_RETRIES
        
        for attempt in range(max_retries + 1):
            state.status = "generating"
            self._update_ask_result(state.query_id, "generating")
            
            # 生成 SQL
            error_feedback = None
            if attempt > 0 and state.validation_result:
                error_feedback = state.validation_result.error_message
            
            generation_result = await self.generation_agent.generate_sql(
                query=state.user_query,
                table_ddls=state.schema_result.table_ddls,
                reasoning=state.reasoning_result.reasoning,
                histories=state.histories,
                configuration=state.configuration,
                has_calculated_field=state.schema_result.has_calculated_field,
                has_metric=state.schema_result.has_metric,
                error_feedback=error_feedback
            )
            
            state.generation_result = generation_result
            
            # 验证 SQL
            await self._step_sql_validation(state)
            
            if state.validation_result.is_valid:
                # 验证成功，完成工作流
                await self._complete_sql_workflow(state)
                return
            else:
                # 验证失败，记录错误并准备重试
                state.error_messages.append(state.validation_result.error_message)
                state.retry_count = attempt + 1
                
                if attempt < max_retries:
                    # 如果是生成错误，回到推理步骤重新推理
                    if state.validation_result.error_type in ["SYNTAX", "EXECUTION"]:
                        await self._step_sql_reasoning_with_feedback(state)
                else:
                    # 达到最大重试次数，标记为失败
                    await self._handle_sql_generation_failure(state)
                    return
