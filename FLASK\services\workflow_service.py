import logging
import uuid
import async<PERSON>
from typing import Dict, Optional, List
from cachetools import TTLCache
from config import Config
from models.data_models import WorkflowState, IntentResult, AskHistory, Configuration
from models.response_models import AskResultResponse, AskResult, AskError
from agents import (
    IntentAgent,
    SchemaRetrievalAgent,
    SqlGenerationAgent,
    SqlValidationAgent,
    HistoricalQuestionAgent,
    SqlPairsRetrievalAgent,
    InstructionsRetrievalAgent,
    SqlFunctionsAgent
)

logger = logging.getLogger(__name__)


class WorkflowService:
    """工作流服务 - 协调各个 Agent 的执行"""

    def __init__(self):
        # 初始化所有 Agent
        self.intent_agent = IntentAgent()
        self.schema_agent = SchemaRetrievalAgent()
        self.generation_agent = SqlGenerationAgent()
        self.validation_agent = SqlValidationAgent()
        self.historical_question_agent = HistoricalQuestionAgent()
        self.sql_pairs_agent = SqlPairsRetrievalAgent()
        self.instructions_agent = InstructionsRetrievalAgent()
        self.sql_functions_agent = SqlFunctionsAgent()

        # 结果缓存
        self.workflow_states: Dict[str, WorkflowState] = TTLCache(
            maxsize=Config.CACHE_MAXSIZE,
            ttl=Config.CACHE_TTL
        )

        self.ask_results: Dict[str, AskResultResponse] = TTLCache(
            maxsize=Config.CACHE_MAXSIZE,
            ttl=Config.CACHE_TTL
        )

    async def process_ask_request(
        self,
        query: str,
        project_id: Optional[str] = None,
        mdl_hash: Optional[str] = None,
        thread_id: Optional[str] = None,
        histories: list[AskHistory] = None,
        configurations: Configuration = None,
        ignore_sql_generation_reasoning: bool = False,
        enable_column_pruning: bool = False
    ) -> str:
        """处理用户问题请求"""

        query_id = str(uuid.uuid4())

        # 初始化工作流状态
        state = WorkflowState(
            query_id=query_id,
            user_query=query,
            project_id=project_id,
            histories=histories or [],
            configuration=configurations or Configuration(),
            status="understanding"
        )

        self.workflow_states[query_id] = state
        self.ask_results[query_id] = AskResultResponse(status="understanding")

        # 异步执行工作流
        import asyncio
        asyncio.create_task(self._execute_workflow(query_id))

        return query_id

    async def _execute_workflow(self, query_id: str):
        """执行完整的工作流"""

        try:
            state = self.workflow_states[query_id]

            # 步骤1: 意图分类
            await self._step_intent_classification(state)

            # 根据意图决定后续流程
            if state.intent_result.intent == "SQL_QUERY":
                await self._execute_sql_workflow(state)
            elif state.intent_result.intent == "GENERAL":
                await self._execute_general_workflow(state)
            else:  # MISLEADING_QUERY
                await self._execute_misleading_workflow(state)

        except Exception as e:
            logger.error(f"Workflow execution failed for {query_id}: {e}")
            await self._handle_workflow_error(query_id, str(e))

    async def _step_intent_classification(self, state: WorkflowState):
        """步骤1: 意图分类"""

        state.status = "understanding"
        self._update_ask_result(state.query_id, "understanding")

        # 调用意图分类 Agent
        intent_result = await self.intent_agent.classify_intent(
            query=state.user_query,
            histories=state.histories,
            project_id=state.project_id,
            configuration=state.configuration
        )

        state.intent_result = intent_result

        # 如果有重新表述的问题，更新查询
        if intent_result.rephrased_question:
            state.user_query = intent_result.rephrased_question

    async def _execute_sql_workflow(self, state: WorkflowState):
        """执行 SQL 查询工作流"""

        # 步骤1: 历史问题检索
        await self._step_historical_question_retrieval(state)

        # 如果找到相似的历史问题，直接返回
        if state.historical_question_result:
            await self._complete_historical_workflow(state)
            return

        # 步骤2: 并行资源检索
        await self._step_parallel_retrieval(state)

        # 步骤3: 表结构检索
        await self._step_schema_retrieval(state)

        # 步骤4: SQL 生成与验证（带重试机制）
        await self._step_sql_generation_with_retry(state)

    async def _step_schema_retrieval(self, state: WorkflowState):
        """步骤2: 表结构检索"""

        state.status = "searching"
        self._update_ask_result(state.query_id, "searching")

        schema_result = await self.schema_agent.retrieve_schema(
            query=state.user_query,
            project_id=state.project_id
        )

        state.schema_result = schema_result



    async def _step_sql_generation_with_retry(self, state: WorkflowState):
        """步骤4: SQL 生成与验证（带重试机制）"""

        max_retries = Config.MAX_SQL_CORRECTION_RETRIES

        for attempt in range(max_retries + 1):
            state.status = "generating"
            self._update_ask_result(state.query_id, "generating")

            # 生成 SQL
            error_feedback = None
            if attempt > 0 and state.validation_result:
                error_feedback = state.validation_result.error_message

            generation_result = await self.generation_agent.generate_sql(
                query=state.user_query,
                table_ddls=state.schema_result.table_ddls,
                histories=state.histories,
                sql_samples=state.sql_samples,
                instructions=state.instructions,
                configuration=state.configuration,
                has_calculated_field=state.schema_result.has_calculated_field,
                has_metric=state.schema_result.has_metric,
                sql_functions=state.sql_functions,
                error_feedback=error_feedback
            )

            state.generation_result = generation_result

            # 验证 SQL
            await self._step_sql_validation(state)

            if state.validation_result.is_valid:
                # 验证成功，完成工作流
                await self._complete_sql_workflow(state)
                return
            else:
                # 验证失败，记录错误并准备重试
                state.error_messages.append(state.validation_result.error_message)
                state.retry_count = attempt + 1

                if attempt >= max_retries:
                    # 达到最大重试次数，标记为失败
                    await self._handle_sql_generation_failure(state)
                    return

                # 继续重试，error_feedback 会在下次循环中使用

    async def _step_historical_question_retrieval(self, state: WorkflowState):
        """步骤1: 历史问题检索"""

        try:
            historical_results = await self.historical_question_agent.search_similar_questions(
                question=state.user_query,
                project_id=state.project_id,
                similarity_threshold=Config.SIMILARITY_THRESHOLD,
                max_results=Config.MAX_HISTORICAL_RESULTS
            )

            if historical_results:
                state.historical_question_result = historical_results
                logger.info(f"Found {len(historical_results)} similar historical questions")
            else:
                logger.info("No similar historical questions found")

        except Exception as e:
            logger.error(f"Historical question retrieval failed: {e}")
            # 不影响主流程，继续执行

    async def _step_parallel_retrieval(self, state: WorkflowState):
        """步骤2: 并行资源检索"""

        try:
            # 并行执行三个检索任务
            sql_samples, instructions, sql_functions = await asyncio.gather(
                self.sql_pairs_agent.retrieve_sql_pairs(
                    query=state.user_query,
                    project_id=state.project_id
                ),
                self.instructions_agent.retrieve_instructions(
                    query=state.user_query,
                    project_id=state.project_id
                ),
                self.sql_functions_agent.retrieve_sql_functions(
                    query=state.user_query,
                    project_id=state.project_id
                ),
                return_exceptions=True
            )

            # 处理结果，忽略异常
            state.sql_samples = sql_samples if not isinstance(sql_samples, Exception) else []
            state.instructions = instructions if not isinstance(instructions, Exception) else []
            state.sql_functions = sql_functions if not isinstance(sql_functions, Exception) else []

            logger.info(f"Retrieved {len(state.sql_samples)} SQL samples, "
                       f"{len(state.instructions)} instructions, "
                       f"{len(state.sql_functions)} SQL functions")

        except Exception as e:
            logger.error(f"Parallel retrieval failed: {e}")
            # 设置默认值
            state.sql_samples = []
            state.instructions = []
            state.sql_functions = []

    async def _step_sql_validation(self, state: WorkflowState):
        """SQL 验证步骤"""

        state.status = "validating"
        self._update_ask_result(state.query_id, "validating")

        validation_result = await self.validation_agent.validate_sql(
            sql=state.generation_result.sql,
            project_id=state.project_id
        )

        state.validation_result = validation_result

    async def _complete_historical_workflow(self, state: WorkflowState):
        """完成历史问题工作流"""

        state.status = "finished"

        # 使用历史问题的SQL作为结果
        historical_result = state.historical_question_result[0]
        ask_result = AskResult(
            sql=historical_result["sql"],
            type="historical"
        )

        response = AskResultResponse(
            status="finished",
            response=[ask_result],
            type="TEXT_TO_SQL",
            rephrased_question=state.intent_result.rephrased_question if state.intent_result else None,
            intent_reasoning=state.intent_result.reasoning if state.intent_result else None,
            retrieved_tables=None
        )

        self.ask_results[state.query_id] = response
        logger.info(f"Completed historical workflow: {state.query_id}")

    async def _complete_sql_workflow(self, state: WorkflowState):
        """完成 SQL 工作流"""

        state.status = "finished"

        # 构建成功响应
        ask_result = AskResult(
            sql=state.generation_result.sql,
            type="llm"
        )

        response = AskResultResponse(
            status="finished",
            response=[ask_result],
            type="TEXT_TO_SQL",
            rephrased_question=state.intent_result.rephrased_question,
            intent_reasoning=state.intent_result.reasoning,
            retrieved_tables=state.schema_result.table_names if state.schema_result else None
        )

        self.ask_results[state.query_id] = response

        # 自动存储成功的问题-SQL对到向量数据库（作为正向反馈）
        try:
            await self.historical_question_agent.store_question_sql_pair(
                question=state.user_query,
                sql=state.generation_result.sql,
                project_id=state.project_id,
                metadata={
                    "query_id": state.query_id,
                    "intent": state.intent_result.intent,
                    "retry_count": state.retry_count,
                    "table_count": len(state.schema_result.table_names) if state.schema_result else 0
                }
            )
            logger.info(f"Stored successful SQL generation to vector DB: {state.query_id}")
        except Exception as e:
            logger.error(f"Failed to store SQL to vector DB: {e}")
            # 不影响主流程，只记录错误

    async def _handle_sql_generation_failure(self, state: WorkflowState):
        """处理 SQL 生成失败"""

        state.status = "failed"

        # 构建错误响应
        error = AskError(
            code="NO_RELEVANT_SQL",
            message=f"Failed to generate valid SQL after {state.retry_count} attempts"
        )

        response = AskResultResponse(
            status="failed",
            error=error,
            type="TEXT_TO_SQL",
            rephrased_question=state.intent_result.rephrased_question,
            intent_reasoning=state.intent_result.reasoning,
            retrieved_tables=state.schema_result.table_names if state.schema_result else None,
            invalid_sql=state.generation_result.sql if state.generation_result else None
        )

        self.ask_results[state.query_id] = response
        logger.error(f"SQL generation failed: {state.query_id}, errors: {state.error_messages}")

    def _update_ask_result(self, query_id: str, status: str):
        """更新查询结果状态"""

        if query_id in self.ask_results:
            self.ask_results[query_id].status = status
        else:
            # 创建初始响应
            response = AskResultResponse(
                status=status,
                response=[],
                type="TEXT_TO_SQL"
            )
            self.ask_results[query_id] = response

    async def get_ask_result(self, query_id: str) -> Optional[AskResultResponse]:
        """获取查询结果"""
        return self.ask_results.get(query_id)

    async def stop_ask_request(self, query_id: str):
        """停止查询请求"""
        if query_id in self.workflow_states:
            self.workflow_states[query_id].status = "stopped"

    async def handle_feedback(self, query_id: str, is_helpful: bool) -> bool:
        """处理用户反馈"""
        try:
            # 获取工作流状态
            state = self.workflow_states.get(query_id)
            if not state:
                logger.warning(f"No workflow state found for feedback: {query_id}")
                return False

            # 更新向量数据库中的反馈
            success = await self.historical_question_agent.update_feedback(
                question=state.user_query,
                is_helpful=is_helpful,
                project_id=state.project_id
            )

            logger.info(f"Feedback processed for {query_id}: helpful={is_helpful}, success={success}")
            return success

        except Exception as e:
            logger.error(f"Failed to handle feedback for {query_id}: {e}")
            return False

    async def store_question_sql_pair(
        self,
        question: str,
        sql: str,
        project_id: Optional[str] = None,
        view_id: Optional[str] = None,
        metadata: Optional[dict] = None
    ) -> str:
        """手动存储问题-SQL对"""
        try:
            doc_id = await self.historical_question_agent.store_question_sql_pair(
                question=question,
                sql=sql,
                project_id=project_id,
                view_id=view_id,
                metadata=metadata
            )

            logger.info(f"Manually stored question-SQL pair: {doc_id}")
            return doc_id

        except Exception as e:
            logger.error(f"Failed to manually store question-SQL pair: {e}")
            raise

    async def get_vector_db_stats(self) -> dict:
        """获取向量数据库统计信息"""
        try:
            stats = await self.historical_question_agent.get_collection_stats()
            return stats
        except Exception as e:
            logger.error(f"Failed to get vector DB stats: {e}")
            return {"error": str(e)}

    async def _execute_general_workflow(self, state: WorkflowState):
        """执行通用问题工作流"""

        state.status = "finished"

        # 根据问题类型生成通用回答
        general_type = self._determine_general_type(state.user_query)

        if general_type == "DATA_ASSISTANCE":
            response_text = self._generate_data_assistance_response(state.user_query)
        else:  # USER_GUIDE
            response_text = self._generate_user_guide_response(state.user_query)

        # 构建通用响应
        response = AskResultResponse(
            status="finished",
            response=[],  # 通用问题不返回SQL
            type="GENERAL",
            rephrased_question=state.intent_result.rephrased_question,
            intent_reasoning=state.intent_result.reasoning,
            general_type=general_type
        )

        self.ask_results[state.query_id] = response
        logger.info(f"Completed general workflow: {state.query_id}, type: {general_type}")

    async def _execute_misleading_workflow(self, state: WorkflowState):
        """执行误导性问题工作流"""

        state.status = "failed"

        # 构建误导性问题的错误响应
        error = AskError(
            code="NO_RELEVANT_DATA",
            message="The question is too vague or contains unclear references. Please provide more specific information about what data you're looking for."
        )

        response = AskResultResponse(
            status="failed",
            error=error,
            type="GENERAL",
            rephrased_question=state.intent_result.rephrased_question,
            intent_reasoning=state.intent_result.reasoning,
            general_type="MISLEADING_QUERY"
        )

        self.ask_results[state.query_id] = response
        logger.info(f"Completed misleading workflow: {state.query_id}")

    async def _handle_workflow_error(self, query_id: str, error_message: str):
        """处理工作流执行错误"""

        # 构建系统错误响应
        error = AskError(
            code="OTHERS",
            message=f"System error occurred during processing: {error_message}"
        )

        response = AskResultResponse(
            status="failed",
            error=error,
            type="TEXT_TO_SQL"
        )

        self.ask_results[query_id] = response

        # 更新工作流状态
        if query_id in self.workflow_states:
            self.workflow_states[query_id].status = "failed"
            self.workflow_states[query_id].error_messages.append(error_message)

        logger.error(f"Workflow error handled for {query_id}: {error_message}")

    def _determine_general_type(self, query: str) -> str:
        """确定通用问题的类型"""

        # 简单的关键词匹配来确定问题类型
        query_lower = query.lower()

        # 数据相关的关键词
        data_keywords = [
            "data", "table", "column", "field", "database", "schema",
            "what data", "what information", "what tables", "what columns",
            "structure", "format", "type", "definition"
        ]

        # 用户指南相关的关键词
        guide_keywords = [
            "how to", "how do i", "help", "guide", "tutorial", "instruction",
            "explain", "show me how", "teach me", "example"
        ]

        # 检查是否包含数据相关关键词
        for keyword in data_keywords:
            if keyword in query_lower:
                return "DATA_ASSISTANCE"

        # 检查是否包含指南相关关键词
        for keyword in guide_keywords:
            if keyword in query_lower:
                return "USER_GUIDE"

        # 默认返回数据协助
        return "DATA_ASSISTANCE"

    def _generate_data_assistance_response(self, query: str) -> str:
        """生成数据协助响应"""

        return """I can help you understand the available data. Here are some common data structures:

**Customer Data:**
- customers table: customer information including names, contact details
- customer_segments: customer categorization data

**Sales Data:**
- orders table: order transactions and details
- order_items: individual items within orders
- products: product catalog and information

**Financial Data:**
- payments: payment transactions
- invoices: billing information

To get specific information about any table structure, you can ask questions like:
- "What columns are in the customers table?"
- "Show me the structure of the orders table"
- "What data is available for products?"

Would you like to know more about any specific data area?"""

    def _generate_user_guide_response(self, query: str) -> str:
        """生成用户指南响应"""

        return """I'm here to help you query your data using natural language. Here's how to use this system:

**How to Ask Questions:**
1. Use natural language to describe what data you want
2. Be specific about the information you're looking for
3. Mention relevant entities like customers, orders, products, etc.

**Example Questions:**
- "How many customers do we have?"
- "What is the total sales amount for this month?"
- "Show me the top 10 products by sales volume"
- "Which customers have placed orders in the last 30 days?"

**Tips for Better Results:**
- Be specific about time periods (e.g., "last month", "this year")
- Mention specific metrics you want (count, sum, average, etc.)
- Include relevant filters or conditions

**What I Can Help With:**
- Generate SQL queries from your questions
- Explain data relationships
- Provide information about available data
- Help you understand query results

Feel free to ask me any data-related question, and I'll do my best to help you find the information you need!"""
