import logging
import uuid
import async<PERSON>
from typing import Dict, Optional, List
from cachetools import TTLCache
from config import Config
from models.data_models import WorkflowState, AskHistory, Configuration
from models.response_models import AskResultResponse, AskResult, AskError
from agents import (
    IntentAgent,
    SchemaRetrievalAgent,
    SqlReasoningAgent,
    SqlGenerationAgent,
    SqlValidationAgent,
    HistoricalQuestionAgent,
    SqlPairsRetrievalAgent,
    InstructionsRetrievalAgent,
    SqlFunctionsAgent
)

logger = logging.getLogger(__name__)


class WorkflowService:
    """工作流服务 - 协调各个 Agent 的执行"""

    def __init__(self):
        # 初始化所有 Agent
        self.intent_agent = IntentAgent()
        self.schema_agent = SchemaRetrievalAgent()
        self.reasoning_agent = SqlReasoningAgent()
        self.generation_agent = SqlGenerationAgent()
        self.validation_agent = SqlValidationAgent()
        self.historical_question_agent = HistoricalQuestionAgent()
        self.sql_pairs_agent = SqlPairsRetrievalAgent()
        self.instructions_agent = InstructionsRetrievalAgent()
        self.sql_functions_agent = SqlFunctionsAgent()

        # 配置参数
        self.max_sql_correction_retries = Config.MAX_SQL_CORRECTION_RETRIES
        self.max_histories = 5

        # 结果缓存
        self.workflow_states: Dict[str, WorkflowState] = TTLCache(
            maxsize=Config.CACHE_MAXSIZE,
            ttl=Config.CACHE_TTL
        )

        self.ask_results: Dict[str, AskResultResponse] = TTLCache(
            maxsize=Config.CACHE_MAXSIZE,
            ttl=Config.CACHE_TTL
        )

    async def process_ask_request(
        self,
        query: str,
        project_id: Optional[str] = None,
        mdl_hash: Optional[str] = None,
        thread_id: Optional[str] = None,
        histories: List[AskHistory] = None,
        configurations: Configuration = None,
        ignore_sql_generation_reasoning: bool = False,
        enable_column_pruning: bool = False
    ) -> str:
        """处理用户问题请求"""

        query_id = str(uuid.uuid4())

        # 初始化工作流状态
        state = WorkflowState(
            query_id=query_id,
            user_query=query,
            project_id=project_id,
            histories=histories or [],
            configuration=configurations or Configuration(),
            status="understanding"
        )

        # 添加额外的配置信息
        state.mdl_hash = mdl_hash
        state.thread_id = thread_id
        state.ignore_sql_generation_reasoning = ignore_sql_generation_reasoning
        state.enable_column_pruning = enable_column_pruning

        self.workflow_states[query_id] = state
        self.ask_results[query_id] = AskResultResponse(status="understanding")

        # 异步执行工作流
        asyncio.create_task(self._execute_workflow(query_id))

        return query_id

    async def _execute_workflow(self, query_id: str):
        """执行完整的工作流"""

        try:
            state = self.workflow_states[query_id]

            # 限制历史记录数量并反转顺序（参考原始逻辑）
            state.histories = state.histories[:self.max_histories][::-1]

            # 步骤1: 历史问题检索
            await self._step_historical_question_retrieval(state)

            # 如果找到历史问题，直接返回
            if state.historical_question_result:
                await self._complete_historical_workflow(state)
                return

            # 步骤2: 并行检索 SQL 样例和指令
            await self._step_parallel_retrieval(state)

            # 步骤3: 意图分类
            await self._step_intent_classification(state)

            # 根据意图决定后续流程
            if state.intent_result.intent == "SQL_QUERY":
                await self._execute_sql_workflow(state)
            elif state.intent_result.intent == "GENERAL":
                await self._execute_general_workflow(state)
            else:  # MISLEADING_QUERY
                await self._execute_misleading_workflow(state)

        except Exception as e:
            logger.error(f"Workflow execution failed for {query_id}: {e}")
            await self._handle_workflow_error(query_id, str(e))

    async def _step_historical_question_retrieval(self, state: WorkflowState):
        """步骤1: 历史问题检索"""

        state.status = "understanding"
        self._update_ask_result(state.query_id, "understanding")

        # 检索历史问题
        historical_questions = await self.historical_question_agent.retrieve_historical_questions(
            query=state.user_query,
            project_id=state.project_id
        )

        state.historical_question_result = historical_questions

    async def _step_parallel_retrieval(self, state: WorkflowState):
        """步骤2: 并行检索 SQL 样例、指令和 SQL 函数"""

        # 并行执行 SQL 样例检索、指令检索和 SQL 函数检索
        sql_samples_task = self.sql_pairs_agent.retrieve_sql_pairs(
            query=state.user_query,
            project_id=state.project_id
        )

        instructions_task = self.instructions_agent.retrieve_instructions(
            query=state.user_query,
            project_id=state.project_id
        )

        sql_functions_task = self.sql_functions_agent.retrieve_sql_functions(
            query=state.user_query,
            project_id=state.project_id
        )

        # 等待三个任务完成
        sql_samples, instructions, sql_functions = await asyncio.gather(
            sql_samples_task,
            instructions_task,
            sql_functions_task
        )

        state.sql_samples = sql_samples
        state.instructions = instructions
        state.sql_functions = sql_functions

    async def _step_intent_classification(self, state: WorkflowState):
        """步骤3: 意图分类"""

        state.status = "understanding"
        self._update_ask_result(state.query_id, "understanding")

        # 调用意图分类 Agent
        intent_result = await self.intent_agent.classify_intent(
            query=state.user_query,
            histories=state.histories,
            sql_samples=state.sql_samples,
            instructions=state.instructions,
            project_id=state.project_id,
            configuration=state.configuration
        )

        state.intent_result = intent_result

        # 如果有重新表述的问题，更新查询
        if intent_result.rephrased_question:
            state.user_query = intent_result.rephrased_question

    async def _complete_historical_workflow(self, state: WorkflowState):
        """完成历史问题工作流"""

        state.status = "finished"

        # 构建历史问题响应
        ask_results = []
        for result in state.historical_question_result:
            ask_result = AskResult(
                sql=result.get("statement"),
                type="view" if result.get("viewId") else "llm",
                viewId=result.get("viewId")
            )
            ask_results.append(ask_result)

        response = AskResultResponse(
            status="finished",
            response=ask_results,
            type="TEXT_TO_SQL"
        )

        self.ask_results[state.query_id] = response

    async def _execute_sql_workflow(self, state: WorkflowState):
        """执行 SQL 查询工作流"""

        # 步骤4: 表结构检索
        await self._step_schema_retrieval(state)

        # 步骤5: SQL 推理（如果需要）
        if not state.ignore_sql_generation_reasoning:
            await self._step_sql_reasoning(state)

        # 步骤6: SQL 生成（带重试机制）
        await self._step_sql_generation_with_retry(state)

    async def _step_schema_retrieval(self, state: WorkflowState):
        """步骤4: 表结构检索"""

        state.status = "searching"
        self._update_ask_result(state.query_id, "searching")

        schema_result = await self.schema_agent.retrieve_schema(
            query=state.user_query,
            project_id=state.project_id,
            configuration=state.configuration,
            enable_column_pruning=state.enable_column_pruning
        )

        state.schema_result = schema_result

    async def _step_sql_reasoning(self, state: WorkflowState):
        """步骤3: SQL 推理"""

        state.status = "reasoning"
        self._update_ask_result(state.query_id, "reasoning")

        reasoning_result = await self.reasoning_agent.generate_reasoning(
            query=state.user_query,
            table_ddls=state.schema_result.table_ddls,
            histories=state.histories,
            configuration=state.configuration
        )

        state.reasoning_result = reasoning_result

    async def _step_sql_generation_with_retry(self, state: WorkflowState):
        """步骤6: SQL 生成（带重试机制）"""

        max_retries = self.max_sql_correction_retries

        for attempt in range(max_retries + 1):
            state.status = "generating"
            self._update_ask_result(state.query_id, "generating")

            # 生成 SQL
            error_feedback = None
            if attempt > 0 and state.validation_result:
                error_feedback = state.validation_result.error_message

            # 获取推理结果（如果有）
            reasoning = ""
            if state.reasoning_result:
                reasoning = state.reasoning_result.reasoning

            generation_result = await self.generation_agent.generate_sql(
                query=state.user_query,
                table_ddls=state.schema_result.table_ddls,
                reasoning=reasoning,
                histories=state.histories,
                sql_samples=state.sql_samples,
                instructions=state.instructions,
                configuration=state.configuration,
                has_calculated_field=state.schema_result.has_calculated_field,
                has_metric=state.schema_result.has_metric,
                sql_functions=state.sql_functions,
                error_feedback=error_feedback
            )

            state.generation_result = generation_result

            # 验证 SQL
            await self._step_sql_validation(state)

            if state.validation_result.is_valid:
                # 验证成功，完成工作流
                await self._complete_sql_workflow(state)
                return
            else:
                # 验证失败，记录错误并准备重试
                state.error_messages.append(state.validation_result.error_message)
                state.retry_count = attempt + 1

                if attempt < max_retries:
                    # 如果是生成错误，回到推理步骤重新推理
                    if (state.validation_result.error_type in ["SYNTAX", "EXECUTION"]
                        and not state.ignore_sql_generation_reasoning):
                        await self._step_sql_reasoning_with_feedback(state)
                else:
                    # 达到最大重试次数，标记为失败
                    await self._handle_sql_generation_failure(state)
                    return

    async def _step_sql_reasoning_with_feedback(self, state: WorkflowState):
        """带错误反馈的 SQL 推理"""

        error_feedback = state.validation_result.error_message if state.validation_result else None

        reasoning_result = await self.reasoning_agent.generate_reasoning(
            query=state.user_query,
            table_ddls=state.schema_result.table_ddls,
            histories=state.histories,
            configuration=state.configuration,
            error_feedback=error_feedback
        )

        state.reasoning_result = reasoning_result

    async def _step_sql_validation(self, state: WorkflowState):
        """步骤5: SQL 验证"""

        state.status = "validating"
        self._update_ask_result(state.query_id, "validating")

        validation_result = await self.validation_agent.validate_sql(
            sql=state.generation_result.sql,
            project_id=state.project_id
        )

        state.validation_result = validation_result

    async def _complete_sql_workflow(self, state: WorkflowState):
        """完成 SQL 工作流"""

        state.status = "finished"

        # 构建成功响应
        ask_result = AskResult(
            sql=state.generation_result.sql,
            type="llm"
        )

        response = AskResultResponse(
            status="finished",
            response=[ask_result],
            type="TEXT_TO_SQL",
            rephrased_question=state.intent_result.rephrased_question,
            intent_reasoning=state.intent_result.reasoning,
            sql_generation_reasoning=state.reasoning_result.reasoning if state.reasoning_result else None,
            retrieved_tables=state.schema_result.table_names if state.schema_result else None
        )

        self.ask_results[state.query_id] = response

    async def _handle_sql_generation_failure(self, state: WorkflowState):
        """处理 SQL 生成失败"""

        state.status = "failed"

        error_message = f"Failed to generate valid SQL after {state.retry_count} attempts. Last error: {state.error_messages[-1] if state.error_messages else 'Unknown error'}"

        response = AskResultResponse(
            status="failed",
            type="TEXT_TO_SQL",
            error=AskError(
                code="NO_RELEVANT_SQL",
                message=error_message
            ),
            rephrased_question=state.intent_result.rephrased_question,
            intent_reasoning=state.intent_result.reasoning,
            sql_generation_reasoning=state.reasoning_result.reasoning if state.reasoning_result else None,
            retrieved_tables=state.schema_result.table_names if state.schema_result else None,
            invalid_sql=state.generation_result.sql if state.generation_result else None
        )

        self.ask_results[state.query_id] = response

    async def _execute_general_workflow(self, state: WorkflowState):
        """执行通用问题工作流"""

        state.status = "finished"

        # 根据问题类型确定通用类型
        general_type = "DATA_ASSISTANCE"
        if "help" in state.user_query.lower() or "guide" in state.user_query.lower():
            general_type = "USER_GUIDE"

        response = AskResultResponse(
            status="finished",
            type="GENERAL",
            response=[],
            general_type=general_type,
            rephrased_question=state.intent_result.rephrased_question,
            intent_reasoning=state.intent_result.reasoning
        )

        self.ask_results[state.query_id] = response

    async def _execute_misleading_workflow(self, state: WorkflowState):
        """执行误导性问题工作流"""

        state.status = "finished"

        response = AskResultResponse(
            status="finished",
            type="GENERAL",
            general_type="MISLEADING_QUERY",
            error=AskError(
                code="NO_RELEVANT_DATA",
                message="The question is unclear or cannot be answered with the available data."
            ),
            rephrased_question=state.intent_result.rephrased_question,
            intent_reasoning=state.intent_result.reasoning
        )

        self.ask_results[state.query_id] = response

    async def _handle_workflow_error(self, query_id: str, error_message: str):
        """处理工作流错误"""

        response = AskResultResponse(
            status="failed",
            error=AskError(
                code="OTHERS",
                message=f"Workflow execution error: {error_message}"
            )
        )

        self.ask_results[query_id] = response

    def _update_ask_result(self, query_id: str, status: str):
        """更新问答结果状态"""

        if query_id in self.ask_results:
            self.ask_results[query_id].status = status
        else:
            self.ask_results[query_id] = AskResultResponse(status=status)

    def get_ask_result(self, query_id: str) -> Optional[AskResultResponse]:
        """获取问答结果"""
        return self.ask_results.get(query_id)

    def stop_workflow(self, query_id: str):
        """停止工作流"""
        if query_id in self.ask_results:
            self.ask_results[query_id].status = "stopped"

        if query_id in self.workflow_states:
            self.workflow_states[query_id].status = "stopped"
