#!/usr/bin/env python3
"""
测试历史问题检索功能
验证 HistoricalQuestionAgent 的各个方法是否正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.historical_question_agent import HistoricalQuestionAgent
from services.vector_db_service import VectorDBService
from config import Config


async def test_historical_question_agent():
    """测试历史问题检索 Agent"""
    
    print("🧪 测试历史问题检索 Agent")
    print("="*60)
    
    try:
        # 初始化 Agent
        agent = HistoricalQuestionAgent()
        print("✅ HistoricalQuestionAgent 初始化成功")
        
        # 测试数据
        test_questions = [
            "How many customers do we have?",
            "What is the total sales amount?",
            "Show me the top 5 products by sales"
        ]
        
        test_sqls = [
            "SELECT COUNT(*) FROM customers",
            "SELECT SUM(total_amount) FROM orders",
            "SELECT product_name, SUM(quantity) FROM order_items GROUP BY product_name ORDER BY SUM(quantity) DESC LIMIT 5"
        ]
        
        # 1. 测试存储问题-SQL对
        print("\n📝 测试存储问题-SQL对...")
        stored_ids = []
        
        for i, (question, sql) in enumerate(zip(test_questions, test_sqls)):
            try:
                doc_id = await agent.store_question_sql_pair(
                    question=question,
                    sql=sql,
                    project_id="test_project",
                    metadata={
                        "test_id": i + 1,
                        "category": "test_data"
                    }
                )
                stored_ids.append(doc_id)
                print(f"   ✅ 存储成功: {question[:50]}... -> {doc_id}")
            except Exception as e:
                print(f"   ❌ 存储失败: {question[:50]}... -> {e}")
        
        print(f"\n📊 成功存储 {len(stored_ids)} 个问题-SQL对")
        
        # 2. 测试搜索相似问题
        print("\n🔍 测试搜索相似问题...")
        
        search_queries = [
            "How many customers are there?",  # 与第一个问题相似
            "What's the total revenue?",      # 与第二个问题相似
            "Show top products",              # 与第三个问题相似
            "What is the weather today?"      # 不相关的问题
        ]
        
        for query in search_queries:
            try:
                results = await agent.search_similar_questions(
                    question=query,
                    project_id="test_project",
                    similarity_threshold=Config.SIMILARITY_THRESHOLD,
                    max_results=Config.MAX_HISTORICAL_RESULTS
                )
                
                print(f"\n   查询: {query}")
                if results:
                    print(f"   找到 {len(results)} 个相似问题:")
                    for j, result in enumerate(results, 1):
                        similarity = result.get("similarity", 0)
                        question = result.get("question", "")
                        sql = result.get("sql", "")
                        print(f"     {j}. 相似度: {similarity:.3f}")
                        print(f"        问题: {question[:60]}...")
                        print(f"        SQL: {sql[:60]}...")
                else:
                    print("   ❌ 未找到相似问题")
                    
            except Exception as e:
                print(f"   ❌ 搜索失败: {query} -> {e}")
        
        # 3. 测试反馈更新
        print("\n👍 测试反馈更新...")
        
        for i, question in enumerate(test_questions[:2]):  # 只测试前两个
            try:
                is_helpful = i % 2 == 0  # 交替设置有用/无用
                success = await agent.update_feedback(
                    question=question,
                    is_helpful=is_helpful,
                    project_id="test_project"
                )
                
                feedback_text = "有用" if is_helpful else "无用"
                status_text = "成功" if success else "失败"
                print(f"   {status_text}: {question[:50]}... -> {feedback_text}")
                
            except Exception as e:
                print(f"   ❌ 反馈更新失败: {question[:50]}... -> {e}")
        
        # 4. 测试获取统计信息
        print("\n📊 测试获取统计信息...")
        
        try:
            stats = await agent.get_collection_stats()
            print(f"   ✅ 统计信息获取成功:")
            print(f"      总记录数: {stats.get('total_records', 0)}")
            print(f"      集合名称: {stats.get('collection_name', 'N/A')}")
            print(f"      嵌入模型: {stats.get('embedding_model', 'N/A')}")
            print(f"      相似度阈值: {stats.get('similarity_threshold', 0)}")
            
        except Exception as e:
            print(f"   ❌ 统计信息获取失败: {e}")
        
        # 5. 测试检索历史问题（原有方法）
        print("\n🔄 测试检索历史问题（原有方法）...")
        
        try:
            historical_results = await agent.retrieve_historical_questions(
                query="How many customers",
                project_id="test_project"
            )
            
            print(f"   ✅ 检索到 {len(historical_results)} 个历史问题")
            for i, result in enumerate(historical_results[:2], 1):  # 只显示前2个
                question = result.get("question", "")
                similarity = result.get("similarity", 0)
                print(f"     {i}. {question[:50]}... (相似度: {similarity:.3f})")
                
        except Exception as e:
            print(f"   ❌ 历史问题检索失败: {e}")
        
        print("\n🎉 历史问题检索 Agent 测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        return False


async def test_vector_db_service():
    """测试向量数据库服务"""
    
    print("\n🗄️ 测试向量数据库服务")
    print("="*60)
    
    try:
        # 初始化向量数据库服务
        vector_db = VectorDBService()
        print("✅ VectorDBService 初始化成功")
        
        # 测试存储
        print("\n📝 测试直接存储...")
        doc_id = await vector_db.store_question_sql_pair(
            question="Test question for vector DB",
            sql="SELECT * FROM test_table",
            project_id="test_project"
        )
        print(f"   ✅ 存储成功: {doc_id}")
        
        # 测试搜索
        print("\n🔍 测试直接搜索...")
        results = await vector_db.search_similar_questions(
            question="Test question",
            project_id="test_project",
            n_results=3
        )
        print(f"   ✅ 搜索成功: 找到 {len(results)} 个结果")
        
        # 测试统计
        print("\n📊 测试统计信息...")
        stats = await vector_db.get_collection_stats()
        print(f"   ✅ 统计成功: {stats.get('total_records', 0)} 条记录")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 向量数据库服务测试失败: {e}")
        return False


async def test_config_values():
    """测试配置值"""
    
    print("\n⚙️ 测试配置值")
    print("="*60)
    
    config_items = [
        ("SIMILARITY_THRESHOLD", Config.SIMILARITY_THRESHOLD),
        ("MAX_HISTORICAL_RESULTS", Config.MAX_HISTORICAL_RESULTS),
        ("CHROMA_COLLECTION_NAME", Config.CHROMA_COLLECTION_NAME),
        ("EMBEDDING_MODEL", Config.EMBEDDING_MODEL),
        ("CHROMA_PERSIST_DIRECTORY", Config.CHROMA_PERSIST_DIRECTORY)
    ]
    
    for name, value in config_items:
        print(f"   {name}: {value}")
    
    print("✅ 配置值检查完成")
    return True


async def main():
    """主函数"""
    
    print("🚀 开始测试历史问题检索功能")
    print("="*80)
    
    # 运行测试
    tests = [
        ("配置值测试", test_config_values),
        ("向量数据库服务测试", test_vector_db_service),
        ("历史问题检索 Agent 测试", test_historical_question_agent)
    ]
    
    success_count = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if await test_func():
                success_count += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "="*80)
    print(f"📊 测试总结: {success_count}/{len(tests)} 项测试通过")
    
    if success_count == len(tests):
        print("🎉 所有测试通过！历史问题检索功能正常。")
        return True
    else:
        print("💥 部分测试失败！请检查配置和实现。")
        return False


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
