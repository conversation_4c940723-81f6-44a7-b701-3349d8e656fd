# Wren AI Flask Service

基于 PydanticAI Agent Workflow 的用户问题处理系统，使用 Flask 作为 API 框架，DeepSeek 作为 LLM 提供商。

## 系统架构

### Agent Workflow 设计

系统采用多 Agent 协作的方式处理用户问题：

1. **IntentAgent** - 意图分类 Agent
   - 分析用户问题意图
   - 分类为：SQL_QUERY、GENERAL、MISLEADING_QUERY

2. **SchemaRetrievalAgent** - 表结构检索 Agent
   - 根据用户问题检索相关表结构
   - 识别计算字段和指标

3. **SqlReasoningAgent** - SQL 推理 Agent
   - 基于问题和表结构进行逻辑推理
   - 生成解决方案的步骤计划

4. **SqlGenerationAgent** - SQL 生成 Agent
   - 根据推理结果生成 SQL 查询
   - 支持错误反馈重新生成

5. **SqlValidationAgent** - SQL 验证 Agent
   - 验证 SQL 语法和执行可行性
   - 提供详细的错误信息

### 错误处理和重试机制

- 当 SQL 生成失败时，系统会将错误信息反馈给 SqlReasoningAgent 重新推理
- 当 SQL 验证失败时，系统会将验证错误反馈给 SqlGenerationAgent 重新生成
- 支持最大重试次数配置（默认 3 次）

## 安装和配置

### 1. 安装依赖

```bash
cd F:\buster\wren-ai-service\FLASK
pip install -r requirements.txt
```

### 2. 配置环境变量

复制 `.env.example` 为 `.env` 并配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，设置 DeepSeek API 密钥：

```env
DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

### 3. 启动服务

```bash
python app.py
```

服务将在 `http://localhost:5000` 启动。

## API 接口

### 1. 提交问题

**POST** `/v1/asks`

```json
{
    "query": "How many customers do we have?",
    "project_id": "optional_project_id",
    "histories": [],
    "configurations": {
        "language": "English"
    }
}
```

**响应：**
```json
{
    "query_id": "uuid-string"
}
```

### 2. 获取结果

**GET** `/v1/asks/{query_id}/result`

**响应：**
```json
{
    "status": "finished",
    "response": [
        {
            "sql": "SELECT COUNT(*) FROM customers",
            "type": "llm"
        }
    ],
    "rephrased_question": null,
    "intent_reasoning": "User wants to count customers",
    "sql_generation_reasoning": "Need to count all rows in customers table"
}
```

### 3. 获取状态

**GET** `/v1/asks/{query_id}/status`

**响应：**
```json
{
    "query_id": "uuid-string",
    "status": "generating",
    "has_result": false,
    "has_error": false
}
```

### 4. 停止处理

**PATCH** `/v1/asks/{query_id}`

```json
{
    "status": "stopped"
}
```

### 5. 调试信息（仅开发模式）

**GET** `/v1/debug/workflow/{query_id}`

## 状态说明

- `understanding` - 正在理解问题意图
- `searching` - 正在检索相关表结构
- `reasoning` - 正在进行 SQL 推理
- `generating` - 正在生成 SQL
- `validating` - 正在验证 SQL
- `finished` - 处理完成
- `failed` - 处理失败
- `stopped` - 已停止

## 配置说明

### DeepSeek 配置

- `DEEPSEEK_API_KEY`: DeepSeek API 密钥
- `DEEPSEEK_BASE_URL`: API 基础 URL（默认：https://api.deepseek.com/v1）
- `DEEPSEEK_MODEL`: 使用的模型（默认：deepseek-chat）

### 重试配置

- `MAX_SQL_CORRECTION_RETRIES`: 最大重试次数（默认：3）
- `ENGINE_TIMEOUT`: 引擎超时时间（默认：30.0 秒）

### 缓存配置

- `CACHE_TTL`: 缓存生存时间（默认：120 秒）
- `CACHE_MAXSIZE`: 缓存最大大小（默认：1000000）

## 开发说明

### 添加新的 Agent

1. 在 `agents/` 目录下创建新的 Agent 类
2. 继承 `BaseAgent` 类
3. 实现具体的业务逻辑
4. 在 `WorkflowService` 中集成新的 Agent

### 修改工作流

在 `services/workflow_service.py` 中修改 `_execute_workflow` 方法来调整工作流逻辑。

### 错误处理

所有 Agent 都应该实现适当的错误处理，并返回有意义的错误信息。

## 快速开始

### 方式一：直接运行

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，设置 DEEPSEEK_API_KEY

# 3. 启动服务
python start.py
```

### 方式二：使用 Docker

```bash
# 1. 构建镜像
docker build -t wren-ai-flask .

# 2. 运行容器
docker run -p 5000:5000 -e DEEPSEEK_API_KEY=your_key wren-ai-flask
```

### 方式三：使用 Docker Compose

```bash
# 1. 设置环境变量
export DEEPSEEK_API_KEY=your_key

# 2. 启动服务
docker-compose up -d
```

## 测试服务

```bash
# 运行测试脚本
python test_api.py

# 或运行使用示例
python example_usage.py
```

## 注意事项

1. 确保 DeepSeek API 密钥有效且有足够的配额
2. 在生产环境中关闭 DEBUG 模式
3. 根据实际需求调整重试次数和超时时间
4. 定期清理缓存以避免内存泄漏
5. 建议在生产环境中使用反向代理（如 Nginx）
6. 监控日志文件大小，定期轮转日志
