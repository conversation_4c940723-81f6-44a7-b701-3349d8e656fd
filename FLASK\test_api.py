#!/usr/bin/env python3
"""
测试 Wren AI Flask Service 的脚本
"""

import requests
import json
import time
import sys


class WrenAITester:
    def __init__(self, base_url="http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()

    def test_health(self):
        """测试健康检查"""
        print("🔍 Testing health check...")
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ Health check passed")
                print(f"   Response: {response.json()}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False

    def test_ask_workflow(self, query="How many customers do we have?"):
        """测试完整的问答工作流"""
        print(f"\n🤖 Testing ask workflow with query: '{query}'")

        # 1. 提交问题
        ask_data = {
            "query": query,
            "project_id": "test_project",
            "configurations": {
                "language": "English"
            }
        }

        try:
            print("📤 Submitting question...")
            response = self.session.post(
                f"{self.base_url}/v1/asks",
                json=ask_data,
                headers={"Content-Type": "application/json"}
            )

            if response.status_code != 200:
                print(f"❌ Failed to submit question: {response.status_code}")
                print(f"   Response: {response.text}")
                return False

            result = response.json()
            query_id = result.get("query_id")
            print(f"✅ Question submitted successfully")
            print(f"   Query ID: {query_id}")

            # 2. 轮询结果
            return self._poll_result(query_id)

        except Exception as e:
            print(f"❌ Ask workflow error: {e}")
            return False

    def _poll_result(self, query_id, max_wait=60, poll_interval=2):
        """轮询查询结果"""
        print(f"\n⏳ Polling result for query {query_id}...")

        start_time = time.time()
        while time.time() - start_time < max_wait:
            try:
                # 检查状态
                status_response = self.session.get(f"{self.base_url}/v1/asks/{query_id}/status")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    status = status_data.get("status")
                    print(f"   Status: {status}")

                    if status in ["finished", "failed", "stopped"]:
                        # 获取完整结果
                        result_response = self.session.get(f"{self.base_url}/v1/asks/{query_id}/result")
                        if result_response.status_code == 200:
                            result_data = result_response.json()
                            self._print_result(result_data)
                            return status == "finished"
                        else:
                            print(f"❌ Failed to get result: {result_response.status_code}")
                            return False

                time.sleep(poll_interval)

            except Exception as e:
                print(f"❌ Polling error: {e}")
                return False

        print(f"⏰ Timeout after {max_wait} seconds")
        return False

    def _print_result(self, result_data):
        """打印结果"""
        print("\n📋 Final Result:")
        print(f"   Status: {result_data.get('status')}")

        if result_data.get('error'):
            error = result_data['error']
            print(f"   ❌ Error: {error.get('code')} - {error.get('message')}")

        if result_data.get('response'):
            for i, response in enumerate(result_data['response']):
                print(f"   📝 SQL {i+1}: {response.get('sql')}")
                print(f"      Type: {response.get('type')}")

        if result_data.get('rephrased_question'):
            print(f"   🔄 Rephrased: {result_data['rephrased_question']}")

        if result_data.get('intent_reasoning'):
            print(f"   🧠 Intent: {result_data['intent_reasoning'][:100]}...")

        if result_data.get('sql_generation_reasoning'):
            print(f"   💭 Reasoning: {result_data['sql_generation_reasoning'][:100]}...")

    def test_debug_endpoint(self, query_id):
        """测试调试端点"""
        print(f"\n🔧 Testing debug endpoint for query {query_id}...")
        try:
            response = self.session.get(f"{self.base_url}/v1/debug/workflow/{query_id}")
            if response.status_code == 200:
                debug_data = response.json()
                print("✅ Debug info retrieved")
                print(f"   Status: {debug_data.get('status')}")
                print(f"   Retry count: {debug_data.get('retry_count')}")
                if debug_data.get('error_messages'):
                    print(f"   Errors: {debug_data['error_messages']}")
                return True
            else:
                print(f"❌ Debug endpoint failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Debug endpoint error: {e}")
            return False

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 Starting Wren AI Flask Service Tests\n")

        # 测试健康检查
        if not self.test_health():
            print("\n❌ Health check failed, stopping tests")
            return False

        # 测试不同类型的查询
        test_queries = [
            "How many customers do we have?",
            "What is the total sales amount for this year?",
            "Show me the top 5 customers by order value",
            "What kind of data do you have available?",
            "This is a meaningless question about nothing",
            "Calculate the average order value per customer"
        ]

        success_count = 0
        for i, query in enumerate(test_queries, 1):
            print(f"\n{'='*50}")
            print(f"Test {i}/{len(test_queries)}")
            if self.test_ask_workflow(query):
                success_count += 1

        print(f"\n{'='*50}")
        print(f"📊 Test Summary: {success_count}/{len(test_queries)} tests passed")

        return success_count == len(test_queries)


def main():
    """主函数"""
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"

    print(f"Testing Wren AI Flask Service at: {base_url}")

    tester = WrenAITester(base_url)
    success = tester.run_all_tests()

    if success:
        print("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
