import logging
import asyncio
from flask import Flask, request, jsonify, render_template
from typing import Dict, Any
import uuid
from config import Config
from services import WorkflowService
from models import (
    AskRequest, AskResponse, AskResultRequest, AskResultResponse,
    StopAskRequest, StopAskResponse, FeedbackRequest, FeedbackResponse,
    StoreQuestionSqlRequest, StoreQuestionSqlResponse, VectorDBStatsResponse
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 创建 Flask 应用
app = Flask(__name__)
app.config.from_object(Config)

# 初始化服务
workflow_service = WorkflowService()


def run_async(coro):
    """在 Flask 中运行异步函数的辅助函数"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


@app.route('/')
def index():
    """前端主页"""
    return render_template('index.html')


@app.route('/demo')
def demo():
    """演示页面"""
    return render_template('demo.html')


@app.route('/health', methods=['GET'])
def health_check():
    """健康检查端点"""
    return jsonify({"status": "healthy", "service": "wren-ai-flask"})


@app.route('/v1/asks', methods=['POST'])
def ask():
    """提交问题请求"""
    try:
        # 解析请求数据
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        # 验证必需字段
        if 'query' not in data:
            return jsonify({"error": "Missing required field: query"}), 400

        # 创建请求对象
        ask_request = AskRequest(**data)

        # 异步处理请求
        query_id = run_async(workflow_service.process_ask_request(
            query=ask_request.query,
            project_id=ask_request.project_id,
            mdl_hash=ask_request.mdl_hash,
            thread_id=ask_request.thread_id,
            histories=ask_request.histories,
            configurations=ask_request.configurations,
            ignore_sql_generation_reasoning=ask_request.ignore_sql_generation_reasoning,
            enable_column_pruning=ask_request.enable_column_pruning
        ))

        # 返回响应
        response = AskResponse(query_id=query_id)
        return jsonify(response.dict())

    except Exception as e:
        logger.error(f"Error in ask endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/v1/asks/<query_id>/result', methods=['GET'])
def get_ask_result(query_id: str):
    """获取问题处理结果"""
    try:
        # 获取结果
        result = workflow_service.get_ask_result(query_id)

        if result is None:
            return jsonify({"error": "Query ID not found"}), 404

        return jsonify(result.model_dump())

    except Exception as e:
        logger.error(f"Error in get_ask_result endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/v1/asks/<query_id>', methods=['PATCH'])
def stop_ask(query_id: str):
    """停止问题处理"""
    try:
        # 解析请求数据
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        stop_request = StopAskRequest(**data)

        # 停止工作流
        workflow_service.stop_workflow(query_id)

        response = StopAskResponse(query_id=query_id)
        return jsonify(response.dict())

    except Exception as e:
        logger.error(f"Error in stop_ask endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/v1/asks/<query_id>/status', methods=['GET'])
def get_ask_status(query_id: str):
    """获取问题处理状态"""
    try:
        result = workflow_service.get_ask_result(query_id)

        if result is None:
            return jsonify({"error": "Query ID not found"}), 404

        return jsonify({
            "query_id": query_id,
            "status": result.status,
            "has_result": len(result.response) > 0,
            "has_error": result.error is not None
        })

    except Exception as e:
        logger.error(f"Error in get_ask_status endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/v1/debug/workflow/<query_id>', methods=['GET'])
def get_workflow_debug(query_id: str):
    """获取工作流调试信息（开发用）"""
    try:
        if not app.config['DEBUG']:
            return jsonify({"error": "Debug endpoint not available in production"}), 403

        state = workflow_service.workflow_states.get(query_id)

        if state is None:
            return jsonify({"error": "Workflow state not found"}), 404

        return jsonify({
            "query_id": state.query_id,
            "status": state.status,
            "user_query": state.user_query,
            "intent_result": state.intent_result.model_dump() if state.intent_result else None,
            "schema_result": state.schema_result.model_dump() if state.schema_result else None,
            "reasoning_result": state.reasoning_result.model_dump() if state.reasoning_result else None,
            "generation_result": state.generation_result.model_dump() if state.generation_result else None,
            "validation_result": state.validation_result.model_dump() if state.validation_result else None,
            "retry_count": state.retry_count,
            "error_messages": state.error_messages
        })

    except Exception as e:
        logger.error(f"Error in get_workflow_debug endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/v1/asks/<query_id>/feedback', methods=['POST'])
def submit_feedback(query_id: str):
    """提交用户反馈"""
    try:
        # 解析请求数据
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        # 验证必需字段
        if 'is_helpful' not in data:
            return jsonify({"error": "Missing required field: is_helpful"}), 400

        is_helpful = data['is_helpful']
        comment = data.get('comment')

        # 处理反馈
        success = run_async(workflow_service.handle_feedback(query_id, is_helpful))

        if success:
            response = FeedbackResponse(
                success=True,
                message="Feedback submitted successfully"
            )
        else:
            response = FeedbackResponse(
                success=False,
                message="Failed to submit feedback"
            )

        return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"Error in submit_feedback endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/v1/vector-db/store', methods=['POST'])
def store_question_sql():
    """手动存储问题-SQL对"""
    try:
        # 解析请求数据
        data = request.get_json()
        if not data:
            return jsonify({"error": "No JSON data provided"}), 400

        # 验证必需字段
        if 'question' not in data or 'sql' not in data:
            return jsonify({"error": "Missing required fields: question, sql"}), 400

        # 创建请求对象
        store_request = StoreQuestionSqlRequest(**data)

        # 存储到向量数据库
        doc_id = run_async(workflow_service.store_question_sql_pair(
            question=store_request.question,
            sql=store_request.sql,
            project_id=store_request.project_id,
            view_id=store_request.view_id,
            metadata=store_request.metadata
        ))

        response = StoreQuestionSqlResponse(
            success=True,
            doc_id=doc_id,
            message="Question-SQL pair stored successfully"
        )

        return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"Error in store_question_sql endpoint: {e}")
        response = StoreQuestionSqlResponse(
            success=False,
            message=f"Failed to store question-SQL pair: {str(e)}"
        )
        return jsonify(response.model_dump()), 500


@app.route('/v1/vector-db/stats', methods=['GET'])
def get_vector_db_stats():
    """获取向量数据库统计信息"""
    try:
        stats = run_async(workflow_service.get_vector_db_stats())

        if "error" in stats:
            return jsonify({"error": stats["error"]}), 500

        response = VectorDBStatsResponse(**stats)
        return jsonify(response.model_dump())

    except Exception as e:
        logger.error(f"Error in get_vector_db_stats endpoint: {e}")
        return jsonify({"error": str(e)}), 500


@app.errorhandler(404)
def not_found(error):
    """404 错误处理"""
    return jsonify({"error": "Endpoint not found"}), 404


@app.errorhandler(500)
def internal_error(error):
    """500 错误处理"""
    logger.error(f"Internal server error: {error}")
    return jsonify({"error": "Internal server error"}), 500


if __name__ == '__main__':
    logger.info("Starting Wren AI Flask Service...")
    logger.info(f"Debug mode: {app.config['DEBUG']}")
    logger.info(f"DeepSeek model: {Config.DEEPSEEK_MODEL}")

    app.run(
        host='0.0.0.0',
        port=5000,
        debug=app.config['DEBUG'],
        threaded=True
    )
